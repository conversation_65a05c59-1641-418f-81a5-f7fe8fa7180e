<template>
  <page-card :title="'夜间用水趋势'">
    <div class="search-box">
      <SearchForm :fields="searchFields" :initial-values="initialValues" />
    </div>

    <div class="chart-container">
      <CommonChart :echart-obj="chartOption" />
    </div>
  </page-card>
</template>

<script lang="ts" setup>
import PageCard from "@/components/PageCard.vue";
import CommonChart from "@/components/CommonChart.vue";
// import dayjs, { Dayjs } from 'dayjs';
import { getNightWater } from "@/api/energy";
import { onMounted } from "vue";
import { ref } from "vue";
import SearchForm from "@/components/SearchForm.vue";
import BuildingMapper from "@/utils/buildingMapper";

const builds = ref(BuildingMapper.getAllBuildings());
const floors = ref([] as any[]);

const searchFields = ref([
  {
    type: "select" as const,
    prop: "buildId",
    formItem: { label: "建筑" },
    attrs: {
      size: "large",
      placeholder: "请选择",
    },
    options: builds.value,
    on: {
      change: (val: any) => {
        initialValues.value.buildId = val;
        initialValues.value.floor = "";
        floors.value = BuildingMapper.getFloorsByBuildingId(val);
        searchFields.value[1].options = floors.value;
        getCurrentData();
      },
    },
  },
  {
    type: "select" as const,
    prop: "floor",
    formItem: { label: "楼层" },
    attrs: {
      size: "large",
      placeholder: "请选择",
    },
    options: floors.value,
    on: {
      change: (val: any) => {
        initialValues.value.floor = val;
        getCurrentData();
      },
    },
  },
  {
    type: "datePicker" as const,
    prop: "queryDate",
    formItem: { label: "日期" },
    attrs: {
      size: "large",
      placeholder: "请选择日期",
      format: "YYYY-MM-DD",
    },
    on: {
      change: (val: any) => {
        console.log("%c Line:201 🍩 val", "color:#fca650", val);
        initialValues.value.queryDate = val;
        getCurrentData();
      },
    },
  },
]);

const initialValues = ref({
  buildId: builds.value[0]?.id || "",
  floor: "",
  queryDate: "",
});

const getCurrentData = async () => {
  const params = { ...initialValues.value };
  try {
    let res = await getNightWater(params);
    // res = {
    //   msg: "操作成功",
    //   code: 200,
    //   data: {
    //     unit: "m³",
    //     xAxis: ["01", "02", "03", "04", "05"],
    //     valueArray: [
    //       {
    //         valueAxis: ["1", "1", "2", "2", "30"],
    //         buildName: "A栋",
    //         buildId: "1",
    //       },
    //       {
    //         valueAxis: ["2", "3", "2", "2", "2"],
    //         buildName: "B栋",
    //         buildId: "2",
    //       },
    //       {
    //         valueAxis: ["3", "3", "3", "3", "3"],
    //         buildName: "C栋",
    //         buildId: "3",
    //       },
    //       {
    //         valueAxis: ["4", "4", "4", "4", "4"],
    //         buildName: "D栋",
    //         buildId: "4",
    //       },
    //       {
    //         valueAxis: ["5", "5", "5", "5", "5"],
    //         buildName: "E栋",
    //         buildId: "5",
    //       },
    //     ],
    //   },
    // };
    if (res.code === 200) {
      // 格式化数据用于图表
      chartOption.value.xAxis.data = res.data.xAxis;
      chartOption.value.series = res.data.valueArray.map((item: any) => ({
        data: [],
        name: item.buildName,
        type: "bar",
        itemStyle: {
          borderRadius: [4, 4, 0, 0],
        },
        barWidth: "13%",
      }));

      res.data.xAxis.forEach((item: any, index: number) => {
        // 将res.data.valueArray对应的index数据组成一个新的的数组
        let newArr = res.data.valueArray.map((j: any) => {
          return j.valueAxis[index];
        });
        chartOption.value.series[index].data = newArr;
        chartOption.value.series[index].name = res.data.valueArray[index].buildName;
      });
      // chartOption.value.series.data = res.data.xAxis;
    } else {
      console.error("获取夜间用水数据失败:", res);
    }
    // 你可以在这里处理数据并赋值给 chartOption.value.series
  } catch (error) {
    console.error("夜间用水接口请求失败：", error);
  }
};
const chartOption = ref<any>({
  // // 提示框配置
  tooltip: {
    trigger: "axis", // 坐标轴触发
    axisPointer: {
      type: "shadow", // 阴影指示器
    },
    textStyle: {
      // color: "#fff",
      fontSize: "0.25rem",
    },
    formatter: function (params: any) {
      // 自定义提示框格式
      let result = params[0].name + "<br/>";
      params.forEach((item: any) => {
        result += item.marker + item.seriesName + ": " + item.value + "<br/>";
      });
      return result;
    },
  },
  // // 图例配置
  legend: {
    bottom: "1%",
    verticalAlign: "middle", // 垂直对齐方式
    textStyle: {
      color: "#fff",
      align: "center",
      fontSize: "0.16rem",
    },
    data: [
      {
        name: "A栋",
      },
      {
        name: "B栋",
      },
      {
        name: "C栋",
      },
      {
        name: "D栋",
      },
      {
        name: "E栋",
      },
    ],
    // data: [], // 图例数据，从API获取
    // top: "0%", // 图例位置
    // itemHeight: 20,
    // itemWidth: 30,
    // textStyle: {
    //   fontSize: "0.2rem",
    //   color: "#fff",
    // },
  },
  // // 图表网格配置
  grid: {
    left: "3%", // 左边距
    right: "4%", // 右边距
    bottom: "10%", // 下边距
    top: "8%", // 上边距，为图例留空间
    containLabel: true, // 包含坐标轴标签
  },
  // // X轴配置
  xAxis: {
    type: "category", // 类目轴
    axisLine: {
      show: true,
      lineStyle: {
        color: "rgba(255, 255, 255, 1)",
      },
    },
    axisTick: {
      show: false,
    },
    axisLabel: {
      color: "#fff",
      fontSize: "0.2rem",
      // 标签格式化函数，处理过长的标签
      formatter: function (value: string) {
        if (value.length > 8) {
          return value.substring(0, 5) + "..."; // 超过5个字符显示省略号
        }
        return value;
      },
    },
  },
  yAxis: {
    name: "单位：m³",
    axisLine: {
      show: true,
      lineStyle: {
        color: "rgba(255, 255, 255, 1)",
      },
    },
    axisLabel: {
      color: "#fff",
      fontSize: "0.2rem",
    },
    splitLine: {
      lineStyle: {
        color: "rgba(255, 255, 255, 0.3)",
        // type: "dashed",
      },
    },
  },
  series: [
    // {
    //   itemStyle: {
    //     borderRadius: [4, 4, 0, 0], // 所有柱子都设置圆角
    //   },
    //   barWidth: "13%", // 柱子宽度，分组显示时需要调小
    //   type: "bar",
    // },
    // {
    //   itemStyle: {
    //     borderRadius: [4, 4, 0, 0], // 所有柱子都设置圆角
    //   },
    //   barWidth: "13%",
    //   type: "bar",
    // },
    // {
    //   itemStyle: {
    //     borderRadius: [4, 4, 0, 0], // 所有柱子都设置圆角
    //   },
    //   barWidth: "13%",
    //   type: "bar",
    // },
  ],
});
onMounted(() => {
  floors.value = BuildingMapper.getFloorsByBuildingId(initialValues.value.buildId);
  searchFields.value[1].options = floors.value;
  getCurrentData();
});
</script>

<style lang="less" scoped>
.search-box {
  height: 88px;
  // padding: 16px 16px 0 16px;
  // display: flex;
  // justify-content: space-between;
  // align-items: center;
  font-size: 20px;
  color: #fff;
  span {
    margin-right: 10px;
  }
}
.chart-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 470px;
  // > div {
  //   height: 500px;
  //   flex: 1;
  // }
}
</style>
