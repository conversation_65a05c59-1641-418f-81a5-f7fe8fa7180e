import buildingsData from "@/assets/maps/buildings.json";
interface Floor {
  id: string;
  value: string;
  name: string;
  label: string;
}
interface Building {
  id: string;
  value: string;
  buildId: string;
  name: string;
  label: string;
  floors: Floor[];
}

export default class BuildingMapper {
  private static buildings: Building[] = buildingsData.buildings;

  /**
   * 获取所有建筑
   */
  static getAllBuildings(): Building[] {
    return this.buildings;
  }

  /**
   * 根据建筑ID获取建筑信息
   */
  static getBuildingById(buildingId: string): Building | undefined {
    return this.buildings.find((building) => building.id === buildingId);
  }

  /**
   * 获取指定建筑的所有楼层
   */
  static getFloorsByBuildingId(buildingId: string): Floor[] {
    const building = this.getBuildingById(buildingId);
    return building ? building.floors : [];
  }

  /**
   * 获取指定建筑的指定楼层
   */
  static getFloor(buildingId: string, floorId: string): Floor | undefined {
    const floors = this.getFloorsByBuildingId(buildingId);
    return floors.find((floor) => floor.id === floorId);
  }
}
