<template>
  <PageCard :title="'用电结构'">
    <div class="search-box">
      <!-- <a-button @click="$showDeviceDetail('device-001')">查看设备详情</a-button> -->
      <SearchForm :fields="searchFields" :initialValues="initialValues" />
    </div>

    <div class="chart-container">
      <div class="chart-left">
        <common-chart :echart-obj="chartOption" @chartclick="handleLeftChartClick" />
      </div>
      <div class="chart-right">
        <common-chart v-if="selectedCategory" :echart-obj="detailChartOption" />
      </div>
    </div>
  </PageCard>
</template>

<script setup lang="ts">
import PageCard from "@/components/PageCard.vue";
import SearchForm from "@/components/SearchForm.vue";
import CommonChart from "@/components/CommonChart.vue";
import { ref, reactive, computed, onMounted } from "vue";
import { getElectricityStructure } from "@/api/energy";

// 搜索表单配置
const searchFields = ref([
  {
    type: "radio" as const,
    prop: "timeModel",
    formItem: { label: "" },
    showRight: true,
    attrs: {
      size: "large",
    },
    options: [
      { label: "今日", value: "currentDay" },
      { label: "本月", value: "currentMonth" },
      { label: "本年", value: "currentYear" },
    ],
    on: {
      change: (val: any) => {
        initialValues.value.timeModel = val;
        getCurrentData();
      },
    },
  },
]);
const initialValues = ref({
  timeModel: "currentDay",
});
const electricityData = ref<any[]>([]);
const selectedCategory = ref<string | null>(null);
const getCurrentData = async () => {
  const params = { ...initialValues.value, energyType: "electricity" };
  try {
    const res = await getElectricityStructure(params);
    if (res.code === 200) {
      // 格式化数据用于左侧饼图
      const formattedData = res.data.map((item: any) => ({
        name: item.itemName,
        value: item.itemValue,
        ...item,
      }));
      chartOption.series[0].data = formattedData;
      // 保存完整数据用于右侧图表
      electricityData.value = res.data;
    }
  } catch (error) {
    console.error("加载用电结构数据失败:", error);
  }
};

// 处理左侧图表点击事件
const handleLeftChartClick = (params: any) => {
  if (params.name) {
    selectedCategory.value = params.name;
  }
};

// 重置选择
// const resetSelection = () => {
//   selectedCategory.value = null;
// };

// 左侧总览饼图配置
const chartOption = reactive({
  backgroundColor: "transparent",
  color: [
    {
      type: "linear",
      x: 0,
      y: 0,
      x2: 0,
      y2: 1,
      colorStops: [
        { offset: 0, color: "#ffaf55cf" },
        { offset: 1, color: "#ffaf5517" },
      ],
      global: false,
    },
    {
      type: "linear",
      x: 0,
      y: 0,
      x2: 0,
      y2: 1,
      colorStops: [
        { offset: 0, color: "#25d6ff99" },
        { offset: 1, color: "#5cdffd00" },
      ],
      global: false,
    },
    {
      type: "linear",
      x: 0,
      y: 0,
      x2: 1,
      y2: 1,
      colorStops: [
        { offset: 0, color: "#48EDA8" },
        { offset: 1, color: "#48eda800" },
      ],
    },
    {
      type: "linear",
      x: 0,
      y: 0,
      x2: 1,
      y2: 1,
      colorStops: [
        { offset: 0, color: "#A25CE9" },
        { offset: 1, color: "#a25ce900" },
      ],
    },
  ],
  tooltip: {
    trigger: "item",
    formatter: "{b}: {c}",
    backgroundColor: "rgba(0, 10, 20, 0.8)",
    borderColor: "rgba(255, 255, 255, 0.2)",
    textStyle: {
      color: "#fff",
    },
  },
  series: [
    {
      name: "用电类型",
      type: "pie",
      bottom: "10%",
      emphasis: {
        scaleSize: 20,
      },
      radius: ["45%", "90%"],
      center: ["50%", "50%"],
      avoidLabelOverlap: false,
      itemStyle: {
        borderColor: "rgba(0, 0, 0, 0.1)",
        borderWidth: 2,
      },
      label: {
        show: true,
        position: "outside",
        formatter: "{b}\n{c}",
        color: "#fff",
        fontSize: "0.16rem",
        fontWeight: "bold",
      },
      labelLine: {
        show: true,
        length: 15,
        length2: 10,
        lineStyle: {
          color: "#fff",
        },
      },
      data: [],
    },
    {
      name: "中心文字",
      type: "pie",
      radius: ["0", "30%"],
      center: ["50%", "50%"],
      avoidLabelOverlap: false,
      label: {
        show: true,
        position: "center",
        formatter: "用电\n类型",
        color: "#fff",
        fontSize: "0.16rem",
        fontWeight: "bold",
      },
      labelLine: {
        show: false,
      },
      data: [
        {
          value: 100,
          name: "用电类型",
          itemStyle: { color: "rgba(0, 0, 0, 0)" },
        },
      ],
    },
  ],
});

// 右侧详情图表配置 - 根据选中分类动态生成
const detailChartOption = computed(() => {
  if (!selectedCategory.value) {
    return {};
  }
  const categoryData = electricityData.value.find(
    (item) => item.itemName === selectedCategory.value,
  );

  if (!categoryData || !categoryData.childrenItem) {
    return {};
  }

  // 生成颜色数组
  const colors = [
    "#00a0e9",
    "#40a9ff",
    "#91cc75",
    "#fac858",
    "#ee6666",
    "#73c0de",
    "#3ba272",
    "#fc8452",
  ];

  // 处理内环数据（一级子项）
  const innerData = categoryData.childrenItem.map((item: any, _index: number) => ({
    name: item.itemName,
    value: item.itemValue,
    // itemStyle: { color: colors[index % colors.length] },
  }));

  // 处理外环数据（二级子项）
  const outerData: any[] = [];
  categoryData.childrenItem.forEach((parent: any, parentIndex: number) => {
    if (parent.childrenItem && parent.childrenItem.length) {
      parent.childrenItem.forEach((child: any, childIndex: number) => {
        // 确保值不为0时才显示
        if (Number(child.itemValue) > 0) {
          outerData.push({
            name: child.itemName,
            value: child.itemValue,
            itemStyle: {
              color: colors[(parentIndex * 10 + childIndex) % colors.length],
            },
          });
        }
      });
    }
  });

  return {
    backgroundColor: "transparent",
    title: {
      // text: `${selectedCategory.value}\n总计: ${categoryData.itemValue}`,
      left: "center",
      top: "45%",
      textAlign: "center",
      textStyle: {
        color: "#fff",
        fontSize: "0.2rem",
        fontWeight: "bold",
        lineHeight: 1.5,
      },
    },
    tooltip: {
      trigger: "item",
      formatter: "{b}: {c}",
      backgroundColor: "rgba(0, 10, 20, 0.8)",
      borderColor: "rgba(255, 255, 255, 0.2)",
      textStyle: {
        color: "#fff",
      },
    },
    series: [
      // 内环 - 一级子分类
      {
        name: "一级分类",
        type: "pie",
        radius: ["20%", "50%"],
        center: ["50%", "50%"],
        avoidLabelOverlap: false,
        emphasis: {
          scaleSize: 10,
        },
        itemStyle: {
          borderColor: "rgba(0, 0, 0, 0.1)",
          borderWidth: 2,
        },
        label: {
          show: true,
          position: "inside",
          formatter: "{b}\n{c}",
          color: "#fff",
          fontSize: "0.14rem",
          fontWeight: "bold",
        },
        labelLine: {
          show: false,
        },
        data: innerData,
      },
      // 外环 - 二级子分类
      {
        name: "二级分类",
        type: "pie",
        radius: ["50%", "75%"],
        center: ["50%", "50%"],
        avoidLabelOverlap: false,
        emphasis: {
          scaleSize: 10,
        },
        itemStyle: {
          borderColor: "rgba(0, 0, 0, 0.1)",
          borderWidth: 2,
        },
        label: {
          show: true,
          position: "outside",
          formatter: "{b}\n{c}",
          color: "#fff",
          fontSize: "0.12rem",
        },
        labelLine: {
          show: true,
          length: 15,
          length2: 10,
          lineStyle: {
            color: "#fff",
          },
        },
        data: outerData,
      },
    ],
  };
});

onMounted(() => {
  getCurrentData();
});
</script>

<style lang="less" scoped>
.search-box {
  height: 88px;
  // padding-top: 16px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 16px;

  .reset-btn {
    border-color: #00a0e9;
    color: #00a0e9;

    &:hover {
      border-color: #40a9ff;
      color: #40a9ff;
      background-color: rgba(64, 169, 255, 0.1);
    }
  }

  .time {
    :deep(.ant-radio-button-wrapper) {
      font-size: 18px;
    }
  }
}

.chart-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 370px;

  > div {
    height: 370px;
    flex: 1;
  }
}

.chart-right {
  position: relative;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: rgba(255, 255, 255, 0.6);

  .empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.8;
  }

  .empty-text {
    font-size: 16px;
    text-align: center;
    line-height: 1.5;
  }
}

// 添加点击提示动画
.chart-left {
  position: relative;

  &::after {
    content: "点击查看详情";
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 160, 233, 0.8);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 12px;
    opacity: 0;
    animation: pulse 2s infinite;
    pointer-events: none;
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 0;
    transform: translateX(-50%) translateY(0);
  }
  50% {
    opacity: 1;
    transform: translateX(-50%) translateY(-5px);
  }
}
</style>
