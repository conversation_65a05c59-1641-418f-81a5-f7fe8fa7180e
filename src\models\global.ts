export interface screenSizeModel {
  width: number;
  height: number;
  wideScreen: boolean;
  update: boolean;
}

export interface globalState {
  screenSize: screenSizeModel;
  menuList: Array<any>;
  overMenuList: Array<any>;
  mapVisible: boolean;
  webRtcVisible: boolean;
  showExitBtn: boolean;
  theme: string;
  subMenuCheckedKey: string;
  currentFloor: string;
  showVideoDigtal: boolean;
  userInfo: any;
  isLoggedIn: boolean;
}

export type weatherType = {
  fxDate: string; // 预报时间
  tempMax: string; // 预报当天最高温度
  tempMin: string; // 预报当天最低温度
  iconDay: string; // 图标码
  textDay: string; // 白天天气
  iconNight: string; // 夜间图标码
  textNight: string; // 夜间天气
  dateText?: string; //星期几这种展示
};
export type airType = {
  pubTime: string; //空气质量数据发布时间
  aqi: string; //空气质量指数
  level: string; //空气质量指数等级
  category: string; //空气质量指数级别
  primary: string; // 主要污染物，空气质量为优时返回NA
  pm10: string;
  pm2p5: string;
  no2: string;
  so2: string;
  co: string;
  o3: string;
};
export type weatherListRes = {
  code: string;
  daily: Array<weatherType>;
};

export type airRes = {
  code: string;
  now: airType;
};

/** 柔性用电-发电信息*/
export type powerGeneratitonType = {
  labelName: string; // 标签名
  number: number; // 数值
  unit: string; // 单位
};

export type powerGeneratitonListRes = {
  code: string;
  data: Array<powerGeneratitonType>;
};

/** 物联感知-安全: 安防概览 */
export type securityOverviewType = {
  labelName: string; // 标签
  logo: string; // 图标
  number: number; //个数
  unit: string; //单位
};

/** 数字碳汇：碳排统计*/
export type carbonEmissionStatisticType = {
  labelName: string; // 标签
  number: number; //个数
  unit: string; //单位
};

/**物联感知-物联： 设备概览 */
export type overviewListType = {
  labelName: string; // 标签
  number: number; //个数
  unit: string; //单位
};

export type deviceOverviewListType = {
  deviceType: string;
  logo: string;
  overviewList: Array<overviewListType>;
};

/** 首页-园区设备 */
export type parkDeviceType = {
  labelName: string; // 标签
  logo: string; // 图标
  number: number; //个数
  unit: string; //单位
};

/** 智慧停车-充电桩概览 */
export type chargingStationsOverviewType = {
  labelName: string; // 标签名
  logo: string; // 图标
  number: number; // 数值
  unit: string; // 单位
};

/** 能源管理-时间段 */
export interface TimeModel {
  timeModel: string;
}
