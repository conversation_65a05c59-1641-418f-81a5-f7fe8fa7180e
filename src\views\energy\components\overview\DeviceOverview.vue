<template>
  <PageCard :title="'设备概览'">
    <div class="shebei-overview">
      <div
        :class="['item', { 'focus-border': item.buildId === activeItem }]"
        v-for="item in deviceList"
        :key="item.buildId"
        @click="selectItem(item)"
      >
        <div class="lou">{{ item.buildName }}</div>
        <div class="dian">
          <div>
            <span class="light-blue">{{ item.ele.online }}</span>
            <span>/{{ item.ele.total }}</span>
          </div>
          <div class="fz22">电</div>
        </div>
        <div class="shui">
          <div>
            <span class="light-blue">{{ item.water.online }}</span>
            <span>/{{ item.water.total }}</span>
          </div>
          <div class="fz22">水</div>
        </div>
      </div>
    </div>
  </PageCard>
</template>

<script setup lang="ts">
import PageCard from "@/components/PageCard.vue";
import { ref, inject } from "vue";
import type { Emitter } from "mitt";
import { getDeviceOverview } from "@/api/energy";
import { DeviceOverviewItem } from "@/models/energy";
// 加载设备概览

const deviceList = ref<DeviceOverviewItem[]>([]);
const loadDeviceOverview = async () => {
  try {
    const res = await getDeviceOverview();
    if (res.code === 200) {
      deviceList.value = res.data;
    } else {
      console.log(res);
    }
  } catch (error) {
    console.error("加载设备概览失败:", error);
  }
};
loadDeviceOverview();
// import type {
// DeviceOverviewParams,
// } from "@/models/energy";

const bus = inject<Emitter<any>>("bus") as Emitter<any>;
const activeItem = ref("");
// 全局通信
const selectItem = (item: any) => {
  activeItem.value = item.buildId;
  bus.emit("deviceOverview", item);
};
</script>

<style lang="less" scoped>
.shebei-overview {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-evenly;
  font-size: 30px;
  color: #fff;
  padding-bottom: 24px;
  .item:hover {
    transform: translateY(-2px);
    opacity: 0.8;
  }
  .item {
    width: 400px;
    height: 105px;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: space-evenly;
    background: rgba(11, 41, 77, 1);
    border-top: 3px solid;
    border-image: linear-gradient(90deg, #073e7000 0%, #3e92de 50.27%, #073e7000 100.54%) 1;
    margin-top: 24px;
    cursor: pointer;
    transition: all 0.2s ease;
    border-radius: 8px;
    .lou {
      position: relative;
      width: 100px;
      text-align: center;
      line-height: 115px;
    }
    .lou::after {
      width: 2px;
      height: 75px;
      position: absolute;
      right: 0px;
      top: 20px;
      content: "";
      background: linear-gradient(
        to bottom,
        rgba(255, 255, 255, 0),
        #ccc 48.96%,
        rgba(255, 255, 255, 0)
      );
    }
    .dian,
    .shui {
      width: 120px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      & > div:first-child {
        font-size: 35px;
        font-weight: 700;
        // margin-bottom: 10px;
      }
    }
  }
  // height: 100%;
  .light-blue {
    color: rgba(78, 237, 255, 1);
  }
}
.fz16 {
  font-size: 16px;
}
.fz20 {
  font-size: 20px;
}
.fz22 {
  font-size: 22px;
}
.fz30 {
  font-size: 30px;
}
.fz36 {
  font-size: 36px;
}
</style>
