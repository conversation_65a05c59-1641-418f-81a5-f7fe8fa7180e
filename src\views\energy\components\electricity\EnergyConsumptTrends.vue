<template>
  <PageCard :title="'能耗趋势'">
    <div class="search-box">
      <!-- <a-button type="primary" size="large" @click="viewDetails">详情</a-button> -->
      <SearchForm :fields="searchFields" :initialValues="initialValues" />
    </div>

    <div class="chart-container">
      <common-chart :echart-obj="chartOption" @chartclick="handleLeftChartClick" />
    </div>
    <SankeyModal v-model:visible="visible" />
  </PageCard>
</template>

<script setup lang="ts">
import PageCard from "@/components/PageCard.vue";
import CommonChart from "@/components/CommonChart.vue";
import SearchForm from "@/components/SearchForm.vue";
import SankeyModal from "./SankeyModal.vue";
import { ref, onMounted } from "vue";
import { getEnergyConsumptionTrend } from "@/api/energy";

const searchFields = ref([
  {
    type: "radio" as const,
    prop: "timeModel",
    formItem: { label: "" },
    showRight: true,
    attrs: {
      size: "large",
    },
    options: [
      { label: "今日", value: "currentDay" },
      { label: "本月", value: "currentMonth" },
      { label: "本年", value: "currentYear" },
    ],
    on: {
      change: (val: any) => {
        initialValues.value.timeModel = val;
        getCurrentData();
      },
    },
  },
]);
const initialValues = ref({
  timeModel: "currentDay",
});
const getCurrentData = async () => {
  const params = { ...initialValues.value };
  try {
    const res = await getEnergyConsumptionTrend(params);
    if (res.code === 200) {
      res.data.names.forEach((item: any, index: number) => {
        chartOption.value.series.data[index] = { name: item };
      });
      chartOption.value.series.links = res.data.links;
    } else {
      console.log(res);
    }
  } catch (error) {
    console.error("加载能耗趋势失败:", error);
  }
};

const visible = ref<boolean>(false);
// 处理图表点击事件
const handleLeftChartClick = (_params: any) => {
  console.log("view details");
  visible.value = true;
};

// 能耗趋势
const chartOption = ref({
  tooltip: {
    trigger: "item",
    triggerOn: "mousemove",
  },
  levels: [
    {
      depth: 0,
      itemStyle: {
        color: "#fbb4ae",
      },
      lineStyle: {
        color: "source",
        opacity: 0.6,
      },
    },
  ],
  series: {
    type: "sankey",
    emphasis: {
      focus: "adjacency",
    },
    lineStyle: {
      color: "gradient",
      curveness: 0.7,
    },
    data: [{ name: "总电量" }],
    links: [],
  },
});
onMounted(() => {
  getCurrentData();
});
</script>

<style lang="less" scoped>
.chart-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 480px;
  > div {
    width: 85%;
    height: 420px;
  }
}
.search-box {
  height: 88px;
  // padding: 16px 16px 0 16px;
}

:deep(.ant-modal-content) {
  background: rgb(0, 26, 40, 0.8) !important;
  color: #fff;
}
.details-dialog {
  width: 1440px;
  height: 900px;
}
</style>
