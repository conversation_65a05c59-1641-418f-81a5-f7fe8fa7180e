module.exports = {
  root: true,
  env: {
    node: true,
    "vue/setup-compiler-macros": true,
  },
  extends: [
    "plugin:vue/vue3-essential",
    "eslint:recommended",
    "@vue/typescript/recommended",
    "plugin:prettier/recommended",
    "prettier", // eslint-config-prettier
  ],
  parserOptions: {
    ecmaVersion: 2020,
  },
  rules: {
    "no-console": process.env.NODE_ENV === "production" ? "warn" : "off",
    "no-debugger": process.env.NODE_ENV === "production" ? "warn" : "off",
    "brace-style": "off",
    "@typescript-eslint/no-non-null-assertion": "off",
    "arrow-body-style": "off",
    "operator-linebreak": "off",
    "implicit-arrow-linebreak": "off",
    "import/extensions": "off",
    "max-len": "off",
    "no-unused-expressions": "off",
    "object-curly-newline": [
      "error",
      {
        ObjectPattern: {
          multiline: true,
        },
      },
    ],
    "linebreak-style": ["off", "windows"],
    "@typescript-eslint/explicit-module-boundary-types": "off",
    "@typescript-eslint/no-explicit-any": ["off"],
    // 或仅忽略未使用的函数参数
    "@typescript-eslint/no-unused-vars": ["warn", { argsIgnorePattern: "^_" }],
    "vue/multi-word-component-names": "off",
    "prettier/prettier": [
      1,
      {
        endOfLine: "auto",
      },
    ],
  },
};
