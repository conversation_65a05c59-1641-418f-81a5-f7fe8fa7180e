<template>
  <PageLayout>
    <template v-slot:leftcontainer>
      <AllTrend />
      <ElectricityStructure />
    </template>
    <template v-slot:rightcontainer>
      <TenantsElectricity />
      <EnergyConsumptTrends />
    </template>
  </PageLayout>
</template>

<script lang="ts" setup>
import PageLayout from "@/components/PageLayout.vue";
import AllTrend from "@/views/energy/components/electricity/AllTrend.vue";
import ElectricityStructure from "@/views/energy/components/electricity/ElectricityStructure.vue";
import TenantsElectricity from "@/views/energy/components/electricity/TenantsElectricity.vue";
import EnergyConsumptTrends from "@/views/energy/components/electricity/EnergyConsumptTrends.vue";
</script>
<style scoped lang="less">
.dong {
  li {
    color: #fff;
    font-size: 16px;
    margin-bottom: 5px;
    padding: 8px 16px;
    text-align: center;
    background: #e8e8e84d;
    cursor: pointer;
    pointer-events: auto;
    &:hover {
      background: rgba(0, 0, 0, 0.3);
    }
    &.active {
      background: #00aabd;
    }
  }
}
</style>
