<template>
  <!-- 总体用水趋势 -->
  <page-card :title="'建筑用电趋势（租户）'">
    <div class="electricity-all-trend">
      <div class="search-box">
        <SearchForm :fields="searchFields" :initialValues="initialValues" />
      </div>
      <div class="chart-container">
        <common-chart :echart-obj="chartOption" />
      </div>
    </div>
  </page-card>
</template>

<script lang="ts" setup>
import PageCard from "@/components/PageCard.vue";
import CommonChart from "@/components/CommonChart.vue";
import SearchForm from "@/components/SearchForm.vue";
import { getElectricityStructureDetail } from "@/api/energy";
import { ref, onMounted } from "vue";
const searchFields = ref([
  {
    type: "radio" as const,
    prop: "timeModel",
    formItem: { label: "" },
    showRight: true,
    attrs: {
      size: "large",
    },
    options: [
      { label: "今日", value: "currentDay" },
      { label: "本月", value: "currentMonth" },
      { label: "本年", value: "currentYear" },
    ],
    on: {
      change: (val: any) => {
        initialValues.value.timeModel = val;
        getCurrentData();
      },
    },
  },
]);

const initialValues = ref({
  timeModel: "currentDay",
});
const getCurrentData = async () => {
  const params = { ...initialValues.value };
  try {
    const res = await getElectricityStructureDetail(params);
    if (res.code === 200) {
      res.data.valueArray.forEach((item: any, index: number) => {
        chartOption.value.series[index] = {
          name: item.buildName,
          type: "line",
          symbolSize: 8,
          lineStyle: {
            width: 3,
          },
          data: item.valueAxis,
        };
      });
      chartOption.value.xAxis.data = res.data.xAxis;
    } else {
      console.log(res);
    }
  } catch (error) {
    console.error("加载能耗趋势失败:", error);
  }
};

const chartOption = ref<any>({
  backgroundColor: "transparent",
  grid: {
    left: "10%",
    right: "4%",
    bottom: "14%",
    top: "20%",
    containLabel: true,
  },
  // 图例配置
  legend: {
    // data: ["A栋", "B栋", "C栋", "D栋", "E栋"], // 图例数据，从API获取
    icon: "roundRect",
    bottom: "0%", // 图例位置
    textStyle: {
      fontSize: "0.16rem",
      color: "#fff",
    },
  },
  tooltip: {
    trigger: "axis",
    // textStyle: {
    //   fontSize: "0.2rem",
    //   color: "#fff",
    // },
    // backgroundColor: "rgba(0, 10, 20, 0.8)",
    // borderColor: "rgba(255, 255, 255, 0.2)",
  },
  xAxis: {
    type: "category",
    data: ["1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月"],
    axisLine: {
      lineStyle: {
        color: "rgba(255, 255, 255, 0.3)",
      },
    },
    axisTick: {
      show: false,
    },
    axisLabel: {
      color: "#fff",
      fontSize: "0.2rem",
    },
    // splitLine: {
    //   show: true,
    //   lineStyle: {
    //     color: "rgba(255, 255, 255, 0.1)",
    //     type: "dashed",
    //   },
    // },
  },
  yAxis: {
    type: "value",
    name: "单位：kWh",
    nameGap: 30,
    nameTextStyle: {
      fontSize: "0.2rem",
      color: "#FFFFFF",
    },
    // min: 0,
    // max: 140,
    // interval: 20,
    axisLine: {
      show: true,
      lineStyle: {
        color: "rgba(255, 255, 255, 0.3)",
      },
    },
    axisTick: {
      show: false,
    },
    axisLabel: {
      color: "#fff",
      fontSize: "0.2rem",
    },
    splitLine: {
      lineStyle: {
        color: "rgba(255, 255, 255, 0.3)",
        // type: "dashed",
      },
    },
  },
  series: [
    {
      name: "A栋",
      type: "line",
      symbolSize: 8,
      lineStyle: {
        width: 3,
        color: "#00a0e9",
      },
      data: [8, 22, 28, 28, 30, 36, 50, 62, 68, 84, 74, 38],
    },
  ],
});
onMounted(getCurrentData);
</script>

<style lang="less" scoped>
.chart-container {
  height: 387px;
}
.search-box {
  height: 88px;
  // padding-top: 16px;
  display: flex;
  justify-content: flex-end;
  > div {
    margin-right: 30px;
  }
}
</style>
