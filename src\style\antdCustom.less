// 公共样式
input:focus,
textarea:focus,
select:focus {
  outline: none;
  border: none;
}
// 按钮
.ant-btn.ant-btn-primary {
  background: #70ffff26;
}
// 单选框组
.ant-radio-group {
  .ant-radio-button-wrapper,
  .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
    border: none;
    outline: none;
    background: #073e704d;
    color: #fff;
    label {
      border: none;
      outline: none;
    }
    .ant-radio-button {
      border: none;
      outline: none;
    }
    .ant-radio-button-checked {
      background: #073e70;
      border: none;
      outline: none;
    }
  }
  .ant-radio-button-wrapper-checked {
    border: none;
    background: rgba(255, 255, 255, 0.2);
  }
  .ant-radio-button-wrapper::before {
    border: none;
    background: rgba(255, 255, 255, 0.2);
  }
}
// 下拉框
.ant-select:not(.ant-select-customize-input) .ant-select-selector {
  color: #fff;
  border: 2.3px solid #073e70;
  background: #073e704d;
}
.ant-select-clear {
  border-radius: 50%;
}
.ant-select-arrow {
  color: #fff;
}
.ant-picker-suffix {
  color: #fff;
}
// 导航菜单下拉框样式
.ant-menu-submenu-popup {
  .ant-menu.ant-menu-sub {
    background: #001926;
    .ant-menu-item {
      padding-top: 15px;
      padding-bottom: 15px;
      height: auto;
      text-align: center;
    }
    .ant-menu-item-selected {
      background: #1acee2;
    }
  }
}
// 时间选择器
.ant-picker {
  background: none;
  color: #fff;
}

::selection {
  background: #4eedff;
}

// tree
.ant-tree {
  background: none;
  color: #fff;
  min-width: 305px;
  .ant-tree-checkbox-checked .ant-tree-checkbox-inner {
    border: 1.29px solid #ffffff40;
    background: #ffffff40;
  }
  .ant-tree-checkbox-indeterminate .ant-tree-checkbox-inner::after {
    background: #00000040;
  }
  .ant-tree-node-content-wrapper.ant-tree-node-selected {
    background: none;
  }
  .ant-tree-title {
    color: #ffffff;
  }
  .ant-tree-node-content-wrapper:hover {
    background: rgba(0, 229, 255, 0.8);
  }
}
// table
.ant-table {
  background: inherit;
  .ant-table-placeholder {
    border: none;
    .ant-empty-description {
      color: #fff;
    }
    td {
      border: inherit;
    }
  }
  .ant-table-placeholder:hover {
    .ant-empty-description {
      color: #333;
    }
  }
}

// 分页
.ant-pagination.ant-table-pagination {
  color: #fff;
  padding-right: 16px;
  li,
  a,
  button,
  span {
    color: inherit !important;
  }
  .ant-pagination-item-active {
    background: #4eedff;
    color: inherit;
  }
}
.ant-picker {
  height: 40.33px;
  border: 2.3px solid #073e70;
  background: #073e704d;
}
.ant-picker-input > input {
  color: #fff;
}

// .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled)::before {
//   border-color: none;
//   background: rgba(255, 255, 255, 0.2);
// }
// .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
//   color: #ffffff;
//   background: rgba(0, 229, 255, 0.8);
// }
// .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
//   border-color: #4eedff;
// }
// .ant-radio-button-wrapper-checked:not(
//     [class*=" ant-radio-button-wrapper-disabled"]
//   ).ant-radio-button-wrapper:first-child {
//   border-color: #4eedff;
// }
// .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):first-child {
//   border-color: #4eedff;
// }
// .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
//   color: #ffffff;
//   background: rgba(0, 229, 255, 0.8);
// }
// .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):active {
//   color: #ffffff;
//   background: rgba(0, 229, 255, 0.8);
//   border-color: rgba(0, 229, 255, 0.8);
// }
// .ant-radio-button-wrapper:hover {
//   color: #4eedff;
//   border: none;
//   outline: none;
// }
// .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):hover {
//   color: #ffffff;
//   background: rgba(0, 229, 255, 0.8);
//   border-color: rgba(0, 229, 255, 0.8);
// }
// .ant-radio-group-solid
//   .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):focus-within {
//   box-shadow: 0 0 0 1px #e6f7ff;
// }
