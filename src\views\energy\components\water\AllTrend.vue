<template>
  <!-- 总体用水趋势 -->
  <page-card :title="'总体用水趋势'">
    <div class="water-all-trend">
      <div class="search-box">
        <a-button @click="$showDeviceDetail('AN101')">查看设备详情</a-button>
        <SearchForm :fields="searchFields" :initialValues="initialValues" />
      </div>
      <div class="chart-container">
        <common-chart :echart-obj="chartOption" />
      </div>
    </div>
  </page-card>
</template>

<script lang="ts" setup>
import PageCard from "@/components/PageCard.vue";
import SearchForm from "@/components/SearchForm.vue";
import { getElectricityTrend } from "@/api/energy";
import CommonChart from "@/components/CommonChart.vue";
import { ref, onMounted } from "vue";

const searchFields = ref([
  {
    type: "radio" as const,
    prop: "timeModel",
    formItem: { label: "" },
    showRight: true,
    attrs: {
      size: "large",
    },
    options: [
      { label: "今日", value: "currentDay" },
      { label: "本月", value: "currentMonth" },
      { label: "本年", value: "currentYear" },
    ],
    on: {
      change: (val: any) => {
        initialValues.value.timeModel = val;
        getCurrentData();
      },
    },
  },
]);

const initialValues = ref({
  timeModel: "currentDay",
});
const getCurrentData = async () => {
  const params = { ...initialValues.value, energyType: "water" };
  try {
    const res = await getElectricityTrend(params);
    if (res.code === 200) {
      chartOption.value.xAxis.data = res.data.xAxis;
      chartOption.value.series[0].data = res.data.valueAxis; //数据
      chartOption.value.series[1].data = res.data.compareValueAxis; //同期对比数据数据
    } else {
      console.log(res);
    }
  } catch (error) {
    console.error("加载能耗趋势失败:", error);
  }
};

const chartOption = ref({
  backgroundColor: "transparent",
  grid: {
    left: "8%",
    right: "4%",
    bottom: "8%",
    top: "15%",
    containLabel: true,
  },
  // 图例配置
  legend: {
    icon: "roundRect",
    bottom: "0%", // 图例位置
    height: "20",
    textStyle: {
      fontSize: "0.2rem",
      color: "#fff",
      align: "center",
    },
  },
  tooltip: {
    trigger: "axis",
    textStyle: {
      fontSize: "0.2rem",
      color: "#fff",
    },
    backgroundColor: "rgba(0, 10, 20, 0.8)",
    borderColor: "rgba(255, 255, 255, 0.2)",
  },
  xAxis: {
    type: "category",
    data: [], // X轴数据
    axisLine: {
      lineStyle: {
        color: "rgba(255, 255, 255, 0.3)",
      },
    },
    axisTick: {
      show: false,
    },
    axisLabel: {
      color: "#fff",
      fontSize: "0.2rem",
    },
    // splitLine: {
    //   show: true,
    //   lineStyle: {
    //     color: "rgba(255, 255, 255, 0.1)",
    //     type: "dashed",
    //   },
    // },
  },
  yAxis: {
    type: "value",
    name: "单位：m³",
    nameGap: 30,
    nameTextStyle: {
      fontSize: "0.2rem",
      color: "#FFFFFF",
    },
    // min: 0,
    // max: 140,
    // interval: 20,
    axisLine: {
      show: true,
      lineStyle: {
        color: "rgba(255, 255, 255, 0.3)",
      },
    },
    axisTick: {
      show: false,
    },
    axisLabel: {
      color: "#fff",
      fontSize: "0.2rem",
    },
    splitLine: {
      lineStyle: {
        color: "rgba(255, 255, 255, 0.3)",
        // type: "dashed",
      },
    },
  },
  series: [
    {
      name: "耗水量",
      type: "line",
      smooth: true,
      symbol: "circle",
      symbolSize: 8,
      itemStyle: {
        color: "#00a0e9",
      },
      lineStyle: {
        width: 3,
        color: "#00a0e9",
      },
      data: [], // Y轴数据1
    },
    {
      name: "同期对比曲线",
      type: "line",
      // smooth: true,
      // symbol: "circle",
      symbolSize: 8,
      itemStyle: {
        color: "#ff9f00",
      },
      lineStyle: {
        width: 3,
        color: "#ff9f00",
      },
      data: [], // 同期对比数据
    },
  ],
});
onMounted(() => {
  getCurrentData();
});
</script>

<style lang="less" scoped>
.chart-container {
  height: 470px;
}
.search-box {
  height: 88px;
  // padding-top: 16px;
  display: flex;
  justify-content: flex-end;
  .name,
  .time {
    margin-right: 30px;
    :deep .ant-radio-button-wrapper {
      font-size: 18px;
    }
  }
}
</style>
