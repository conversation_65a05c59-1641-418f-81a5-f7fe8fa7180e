<template>
  <PageLayout showTree>
    <template v-slot:leftcontainer>
      <!-- 设备概览 -->
      <DeviceOverview />
      <!-- 故障报警 -->
      <FaultAlarm />
    </template>
    <template v-slot:rightcontainer>
      <!-- 设备状态 -->
      <DeviceStatus />
      <!-- 历史趋势 -->
      <HistoricalTrend />
    </template>
    <template v-slot:treecontainer>
      <CategoryCheckbox :categoryData="categoryData" />
    </template>
  </PageLayout>
</template>

<script lang="ts" setup>
import PageLayout from "@/components/PageLayout.vue";
import CategoryCheckbox from "@/components/CategoryCheckbox.vue";
import DeviceOverview from "@/views/iot-facilities/components/air-conditioning/DeviceOverview.vue";
import FaultAlarm from "@/views/iot-facilities/components/air-conditioning/FaultAlarm.vue";
import DeviceStatus from "@/views/iot-facilities/components/air-conditioning/DeviceStatus.vue";
import HistoricalTrend from "@/views/iot-facilities/components/air-conditioning/HistoricalTrend.vue";
import { ref } from "vue";
const categoryData = ref({
  冷源: [
    { label: "B2冷冻机房", checked: true },
    { label: "B1循环泵机房", checked: true },
    { label: "A栋RF冷却塔", checked: true },
  ],
  热源: [{ label: "B1锅炉", checked: true }],
  空调: [
    { label: "空调箱", checked: true },
    { label: "新风机", checked: true },
    { label: "风机盘管", checked: true },
  ],
  管路: [
    { label: "空调水管", checked: true },
    { label: "空调风管", checked: true },
  ],
});
</script>

<style scoped></style>
