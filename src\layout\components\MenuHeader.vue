<template>
  <section class="menu-warpper">
    <a-row type="flex" class="menu-title-container" align="middle" justify="center">
      <a-menu v-model:selectedKeys="selectedKeys" mode="horizontal" theme="dark">
        <template v-for="item in leftMenu" :key="item.routeName">
          <a-menu-item v-if="!item.submenuList">{{ item.title }}</a-menu-item>
          <a-sub-menu v-else :key="item.routeName" :title="item.title">
            <template #icon>
              <span class="icon">
                <img
                  v-if="currentPath.includes(item.routeName)"
                  :src="item.checkIcon"
                  alt=""
                  srcset=""
                />
                <img v-else :src="item.icon" alt="" srcset="" />
              </span>
            </template>
            <a-menu-item v-for="subitem in item.submenuList" :key="subitem.routeName">
              <div @click="changeRoute(subitem)" class="btn-name">
                {{ subitem.btnName }}
              </div>
            </a-menu-item>
          </a-sub-menu>
        </template>
      </a-menu>
      <div class="menu-title" @click="back()">
        {{ showTitle }}
      </div>
      <a-menu v-model:selectedKeys="selectedKeys" mode="horizontal" theme="dark">
        <template v-for="item in rightMenu" :key="item.routeName">
          <a-menu-item v-if="!item.submenuList">{{ item.title }}</a-menu-item>
          <a-sub-menu v-else :key="item.routeName" :title="item.title">
            <template #icon>
              <span class="icon">
                <img
                  v-if="selectedKeys[0].includes(item.routeName)"
                  :src="item.checkIcon"
                  alt=""
                  srcset=""
                />
                <img v-else :src="item.icon" alt="" srcset="" />
              </span>
            </template>
            <a-menu-item v-for="subitem in item.submenuList" :key="subitem.routeName">
              <div @click="changeRoute(subitem)" class="btn-name">
                {{ subitem.btnName }}
              </div>
            </a-menu-item>
          </a-sub-menu>
        </template>
      </a-menu>
    </a-row>
    <!-- 气候 -->
    <div class="climate">
      <div class="climate-item">
        <span class="climate-title">气温:</span>
        <span class="climate-content">25°C</span>
      </div>
      <div class="climate-item">
        <span class="climate-title">湿度:</span>
        <span class="climate-content">55%</span>
      </div>
      <div class="climate-item">
        <span class="climate-title">风向:</span>
        <span class="climate-content">东南</span>
      </div>
      <div class="climate-item">
        <span class="climate-title">AQI:</span>
        <span class="climate-content">10 优</span>
      </div>
      <div class="climate-item">
        <span class="climate-title">2025-01-01</span>
        <!-- <span class="climate-content"></span> -->
      </div>
      <div class="climate-item">
        <span class="climate-title">星期四</span>
        <!-- <span class="climate-content"></span> -->
      </div>
      <div class="climate-item">
        <span class="climate-title">17:00:00</span>
        <!-- <span class="climate-content"></span> -->
      </div>
      <!-- 在模板中添加退出按钮 -->
      <div class="logout-btn" @click="handleLogout">
        <a-button type="text" style="color: white">退出登录</a-button>
      </div>
    </div>
  </section>
</template>
<script lang="ts" setup>
import { ref, onMounted, inject, Ref, computed, watch } from "vue";
import type { Emitter } from "mitt";
// import { MailOutlined, AppstoreOutlined, SettingOutlined } from "@ant-design/icons-vue";
import { useRouter } from "vue-router";
import { logout as apiLogout } from "@/api/auth";

interface SubMenuItem {
  routeName: string;
  btnName: string;
}
interface MenuItem {
  routeName: string;
  title: string;
  icon?: string;
  checkIcon?: string;
  submenuList?: SubMenuItem[];
}
const router = useRouter();
const currentRoute = router.currentRoute;
const currentPath = computed(() => currentRoute.value.path);
const checkedPath = ref("");
// const subCheckedRoute = ref("");
// const menuList = computed(() => store.state.menuList);
const showTitle: Ref<string> = ref("世博政务办公社区精准智能化管控平台");

// 根据当前路由设置选中的菜单项
const selectedKeys = computed(() => {
  const current = currentPath.value;
  // 查找匹配的菜单项
  const allMenus = [...leftMenu.value, ...rightMenu.value];
  if (current === "/home") return ["/home"];
  for (const menu of allMenus) {
    if (menu.submenuList) {
      const subItem = menu.submenuList.find((sub) => sub.routeName === current);
      if (subItem) {
        return [subItem.routeName];
      }
    } else if (menu.routeName === current) {
      return [menu.routeName];
    }
  }
  return [];
});

import { useStore } from "vuex";
const store = useStore();
const bus = inject<Emitter<any>>("bus");
// const isHomePage = computed(() => {
//   return route.path === "/home" || route.path === "/";
// });

// const backToHome = () => {
//   router.push("/home");
// };

const leftMenu: Ref<MenuItem[]> = ref([]);
const rightMenu: Ref<MenuItem[]> = ref([]);
const initMenu = () => {
  const menuList = store.state.menuList;
  menuList.forEach((i: MenuItem, index: number) => {
    if (index > 2) {
      rightMenu.value.push(i);
    } else {
      leftMenu.value.push(i);
    }
  });
};
initMenu();

const changeRoute = (item: any) => {
  if (currentPath.value !== item.routeName) {
    bus?.emit("routeChange", item.routeName);
    // 分割字符串，先判断第一个字符是否为/，是的话删掉在分割
    let routeName = "";
    if (item.routeName.startsWith("/")) routeName = item.routeName.slice(1);
    const parts = routeName.split("/");
    parts.forEach((i, index) => {
      parts[index] = i.charAt(0).toUpperCase() + i.slice(1); // 首字母大写
    });
    console.log("%c Line:137 🍞菜单当前的传参是：", "color:#2eafb0", {
      Menu: parts[0],
      [parts[0]]: parts[1],
    });
    window.ue5("SwitchMenu", { Menu: parts[0], [parts[0]]: parts[1] });
    checkedPath.value = item.routeName;
  }
};

onMounted(() => {
  bus?.on("checkedBuilding", (index: string) => {
    showTitle.value = index;
    // type = "XuhuiMap";
  });
  bus?.on("routeChange", (route: string) => {
    if (route === "/home") {
      // showTitle.value = "世博政务办公社区智能化管控平台";
      // type = "ShanghaiMap";
    }
  });
});
const back = () => {
  bus?.emit("routeChange", "/home");
  checkedPath.value = "/home";
  // selectedKeys.value = ["/home"];
  // bus.emit("Return", type);
};

// 监听路由变化更新选中状态
watch(
  currentPath,
  (newPath) => {
    checkedPath.value = newPath;
  },
  { immediate: true },
);

// 在script中添加退出登录方法
const handleLogout = async () => {
  try {
    await apiLogout();
    store.commit("logout");
    router.push("/login");
    // message.success('已退出登录');
  } catch (error) {
    // 即使API调用失败，也要清除本地状态
    store.commit("logout");
    router.push("/login");
  }
};
</script>
<style scoped lang="less">
.menu-warpper {
  width: 100%;
  margin: 0 auto;
  background: url("@/assets/pages/menu_bg.png") 100% no-repeat;
  background-size: cover;
  .menu-title-container {
    pointer-events: auto;
    margin: 0 auto;
  }

  .menu-title {
    line-height: 150px;
    color: #ffffff;
    text-shadow: 0px 9px 25px #002839;
    text-align: center;
    margin: 0 440px 0 400px;
    font-weight: 700;
    font-size: 63px;
  }
  .ant-menu-dark.ant-menu-horizontal {
    font-size: 36px;
    width: auto;
  }
  .climate {
    position: absolute;
    right: 16px;
    top: 40%;
    transform: translateY(-50%);
    display: flex;
    align-items: center;
    .climate-item {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
      margin-right: 24px;
      font-size: 24px;
      color: #a2d9ff;
      .climate-title {
        margin-right: 5px;
      }
    }
  }
  .logout-btn {
    position: absolute;
    right: 16px;
    top: 10px;
    color: white;
    background: #4eedff;
    font-size: 20px;
    cursor: pointer;
    pointer-events: auto;
    &:hover {
      background: rgba(78, 237, 255, 0.5);
    }
  }
}
.btn-name {
  width: 200px;
  font-size: 32px !important;
}
.icon.ant-menu-item-icon {
  .anticon {
    font-size: 35px;
  }
}
:deep(.ant-row) {
  ul:nth-child(1) {
    justify-content: flex-end;
  }
  .ant-menu-overflow.ant-menu.ant-menu-root.ant-menu-horizontal.ant-menu-dark {
    background: none;
    width: 1500px;
    .ant-menu-submenu {
      .ant-menu-submenu-title {
        display: flex;
        align-items: center;
      }
      padding: 15px 50px;
      // background: #1ACEE2;
    }
    .ant-menu-submenu-selected {
      color: aqua;
    }
  }
}
</style>
