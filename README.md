# janke-transport-digital-twin-platform

### 分支介绍

1. 当前的分支是独立于其他所有分支的，是全新的项目，初始没有剥离
2. 当前的项目是是展示在 UE5 虚幻引擎浏览器里面的项目，主要是 UE5+WebUI，而我们的代码就是托管在 webUI 里面的，所以要和 UE 进行通信
   - 前端跟 UE 交互主要有 2 种方式
     1. 像素流，主要以前端为主，就是在我们做成的页面上面放一层像素流的容器，运行在浏览器里面
     2. UE + webUI，主要以 UE 为主，在 UE 里面写好逻辑，写好的页面放在 webUI 里面，然后在 UE 里面调用 webUI 的页面
3. 目前，我们用的就是第二种方法，项目是包管理用的 pnpm
4. 通信代码在 public/UE.js 中，使用方法在 window.ue5(方法名, 参数)，例如：
   ```
   window.ue5("SwitchMenu", { Menu: parts[0], [parts[0]]: parts[1] });
   ```

### 项目结构

```
├── docs                    # 说明文档
├── src
│   ├── api                 # 接口请求
│   ├── assets              # 静态资源
│   ├── components          # 公共组件
│   ├── composables         # Vue3组合式API，通用逻辑
│   ├── examples            # 示例代码
│   ├── layout              # 布局组件
│   ├── mock                # 模拟数据
│   ├── models              # 类型定义
│   ├── router              # 路由配置
│   ├── store               # 状态管理
│   ├── style               # 全局样式
│   ├── utils               # 工具函数
│   ├── views               # 页面组件
│   ├── App.vue             # 根组件
│   ├── main.ts             # 入口文件
│   └── shims-vue.d.ts      # TypeScript类型声明
```

1. 在 assets/maps/buildings.json 中配置建筑和楼层信息，后面有其他的映射关系可以继续写在 maps 文件夹下
2. 在 utils/buildingMapper.ts 中获取建筑和楼层信息

### 兼容 ue4

从 UE4 获取到的浏览器信息如下：

> "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) WebInterface/++UE4+Release-4.27-CL-18319896 UnrealEngine/4.27.2-18319896+++UE4+Release-4.27 Chrome/59.0.3071.15 Safari/537.36"

1. 目前只做了 Chrome 59 版本的兼容，后面如果有 Safari 浏览器，继续做兼容处理
2. 兼容处理查看 _babel.config.js_
