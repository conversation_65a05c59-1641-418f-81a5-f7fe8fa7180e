<!-- 故障报警 -->
<template>
  <PageCard class="fault-alarm" :title="'故障报警'">
    <SearchForm :fields="searchFields" :initialValues="initialValues" />
    <div class="table-container">
      <BaseTable
        class="ant-table-striped"
        :columns="columns"
        :data-source="data"
        :row-class-name="(_record:any, index:number) => (index % 2 === 1 ? 'table-dan' : 'table-shuang')"
        :pagination="{
          total: total,
          current: pageNum,
          pageSize: pageSize,
          onChange: handlePageChange,
        }"
        :bordered="false"
      >
        <template v-slot:column="scoped">
          <a-row
            v-if="scoped.column.dataIndex === 'operation'"
            class="operation"
            align="middle"
            justify="center"
          >
            <img src="@/assets/pages-icon/energy/search1.png" alt="" srcset="" />
            <img src="@/assets/pages-icon/energy/place.png" alt="" srcset="" />
          </a-row>
        </template>
      </BaseTable>
    </div>
  </PageCard>
</template>

<script setup lang="ts">
import PageCard from "@/components/PageCard.vue";
import SearchForm from "@/components/SearchForm.vue";
import BaseTable from "@/components/BaseTable.vue";
import { ref } from "vue";
import { DeviceStatusItem } from "@/models/energy";
import { getDeviceStatusList } from "@/api/energy";

const searchFields = ref([
  {
    type: "select" as const,
    prop: "deviceType",
    formItem: { label: "设备类型" },
    attrs: {
      placeholder: "请选择",
      clearable: true,
      size: "large",
    },
    options: [
      { label: "全部", value: "" },
      { label: "冷水主机", value: "1" },
      { label: "冷却塔", value: "2" },
      { label: "冷冻水循环泵", value: "3" },
      { label: "冷却水循环泵", value: "4" },
      { label: "空调箱", value: "5" },
      { label: "新风机", value: "6" },
      { label: "风机盘管", value: "7" },
      { label: "送风机", value: "8" },
      { label: "排风机", value: "9" },
      { label: "生活给水系统", value: "10" },
      { label: "污水泵", value: "11" },
    ],
    on: {
      change: (val: any) => {
        initialValues.value.deviceType = val;
        // getStatusList();
      },
    },
  },
  {
    type: "select" as const,
    prop: "alarmType",
    formItem: { label: "报警类型" },
    attrs: {
      size: "large",
      placeholder: "请选择",
    },
    options: [
      { label: "全部", value: "" },
      { label: "故障报警", value: "1" },
      { label: "过滤网报警", value: "2" },
    ],
    on: {
      change: (val: any) => {
        initialValues.value.alarmType = val;
        // getStatusList();
      },
    },
  },
  {
    type: "select" as const,
    prop: "isRelieve",
    formItem: { label: "是否解除" },
    attrs: {
      size: "large",
      placeholder: "请选择",
    },
    options: [
      { label: "全部", value: "" },
      { label: "已解除", value: "1" },
      { label: "未解除", value: "2" },
    ],
    on: {
      change: (val: any) => {
        initialValues.value.isRelieve = val;
        // getStatusList();
      },
    },
  },
  {
    type: "radio" as const,
    prop: "timeModel",
    formItem: { label: "" },
    showRight: true,
    attrs: {
      size: "large",
    },
    options: [
      { label: "今日", value: "currentDay" },
      { label: "本月", value: "currentMonth" },
      { label: "本年", value: "currentYear" },
    ],
    on: {
      change: (val: any) => {
        initialValues.value.timeModel = val;
        // getCurrentData();
      },
    },
  },
]);
const initialValues = ref({
  deviceType: "", //设备类型
  alarmType: "", //报警类型
  isRelieve: "", //是否解除
  timeModel: "currentDay",
});

const columns = [
  { title: "报警开始时间", dataIndex: "alarmStartTime" },
  { title: "设备名称", dataIndex: "deviceName" },
  { title: "位置", dataIndex: "location" },
  { title: "报警内容", dataIndex: "alarmContent" },
  { title: "是否解除", dataIndex: "isRelieve" },
  { title: "操作", dataIndex: "operation" },
];

// 获取列表数据
const data = ref<DeviceStatusItem[]>([]);
// 添加分页相关响应式变量
const pageNum = ref(1);
const pageSize = ref(20);
const total = ref(0); // 总条数

// 修改获取列表数据的方法
const getStatusList = async () => {
  try {
    const params = {
      ...initialValues.value,
      pageNum: pageNum.value, // 使用当前页码
      pageSize: pageSize.value, // 使用当前页大小
    };
    const res = await getDeviceStatusList(params);
    if (res.code === 200) {
      res.rows.forEach((item: any) => {
        switch (item.status) {
          case 1:
            item.statusText = "未激活";
            break;
          case 4:
            item.statusText = "离线";
            break;
        }
      });
      data.value = res.rows;
      total.value = res.total; // 从接口响应中获取总条数
    } else {
      console.log(res);
    }
  } catch (error) {
    console.error("加载设备概览失败:", error);
  }
};
// 分页变化处理函数
const handlePageChange = (newPage: number, newPageSize: number) => {
  pageNum.value = newPage; // 更新当前页码
  pageSize.value = newPageSize; // 更新当前页码
  // getStatusList(); // 重新请求数据
};
getStatusList();
</script>

<style lang="less" scoped>
.page-card {
  .search-box {
    height: 88px;
    // padding: 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 20px;
    color: #fff;
    span {
      margin-right: 10px;
    }
  }
  .table-container {
    position: relative;
    height: 670px;
    overflow: hidden;
  }
  :deep .ant-table-striped {
    position: relative;
    left: 10px;
    .table-dan {
      height: 57px;
      margin: 8px;
      td {
        background-color: rgba(7, 27, 51, 1);
        color: #fff;
      }
    }
    .table-shuang {
      height: 57px;
      margin: 8px;
      td {
        background-color: rgba(15, 36, 61, 1);
        color: #fff;
      }
    }
    .ant-table-thead {
      th {
        background-color: rgba(15, 41, 82, 1);
        color: #fff;
        height: 67px;
      }
    }
  }

  .ant-table-striped :deep(.ant-table-body) {
    overflow-y: auto !important;
    max-height: 550px !important;
  }

  .ant-table-striped :deep(.ant-table-body::-webkit-scrollbar) {
    position: absolute;
    left: 10px;
    width: 3px;
    height: 8px;
  }
  img {
    margin-right: 20px;
    cursor: pointer;
  }
  img:hover {
    opacity: 0.7;
  }
}
</style>
