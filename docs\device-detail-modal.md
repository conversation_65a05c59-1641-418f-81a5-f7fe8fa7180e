# 设备详情弹窗组件使用指南

## 概述

设备详情弹窗是一个全局组件，用于显示设备的详细信息。弹窗包含三个标签页：设备信息、数据查询和系统图。

## 功能特性

### 🎯 核心功能
- **设备信息**：显示设备的基本信息列表
- **数据查询**：提供时间范围和数据类型选择，显示折线图
- **系统图**：显示设备的系统图片
- **全局调用**：可在任何地方通过简单的API调用

### 📊 数据支持
- **API集成**：支持真实API数据获取
- **Mock数据**：API不可用时自动使用Mock数据
- **缓存机制**：支持数据缓存，提升性能
- **错误处理**：完善的错误处理和用户提示

## 安装和配置

### 1. 自动注册（已完成）
组件已在 `main.ts` 中自动注册为全局插件：

```typescript
import DeviceModalPlugin from "@/utils/deviceModal";
app.use(DeviceModalPlugin);
```

### 2. API配置
在 `src/api/device.ts` 中配置相关API接口。

## 使用方法

### 1. 基本使用

```typescript
import { showDeviceDetail } from '@/utils/deviceModal';

// 显示设备详情
showDeviceDetail('device-001');
```

### 2. 在Vue组件中使用

```vue
<template>
  <a-button @click="handleShowDevice">查看设备详情</a-button>
</template>

<script setup>
import { showDeviceDetail } from '@/utils/deviceModal';

const handleShowDevice = () => {
  showDeviceDetail('device-001', '空调设备');
};
</script>
```

### 3. 使用全局属性

```vue
<template>
  <a-button @click="$showDeviceDetail('device-001')">
    查看设备详情
  </a-button>
</template>
```

### 4. 使用 inject

```vue
<script setup>
import { inject } from 'vue';

const deviceModal = inject('deviceModal');

const handleShowDevice = () => {
  deviceModal.show('device-001');
};
</script>
```

## API 接口

### showDeviceDetail(deviceId, deviceName?)
显示设备详情弹窗

**参数：**
- `deviceId` (string): 设备ID，必填
- `deviceName` (string): 设备名称，可选

**示例：**
```typescript
showDeviceDetail('device-001');
showDeviceDetail('device-001', '中央空调');
```

### hideDeviceDetail()
隐藏设备详情弹窗

**示例：**
```typescript
hideDeviceDetail();
```

### destroyDeviceDetail()
销毁设备详情弹窗

**示例：**
```typescript
destroyDeviceDetail();
```

## 数据结构

### 设备信息 (DeviceInfo)
```typescript
interface DeviceInfo {
  id: string;           // 设备ID
  name: string;         // 设备名称
  type: string;         // 设备类型
  model: string;        // 设备型号
  manufacturer: string; // 制造商
  installDate: string;  // 安装日期
  location: string;     // 安装位置
  status: string;       // 运行状态
  power: number;        // 额定功率
  voltage: number;      // 额定电压
  current: number;      // 额定电流
}
```

### 数据查询参数 (DeviceDataQuery)
```typescript
interface DeviceDataQuery {
  deviceId: string;     // 设备ID
  startTime: string;    // 开始时间
  endTime: string;      // 结束时间
  dataType: 'power' | 'energy' | 'voltage' | 'current'; // 数据类型
}
```

### 数据查询响应 (DeviceDataResponse)
```typescript
interface DeviceDataResponse {
  times: string[];      // 时间数组
  values: number[];     // 数值数组
  unit: string;         // 单位
  dataType: string;     // 数据类型
}
```

## 配置选项

### 全局配置
```typescript
import { setDeviceModalConfig } from '@/utils/deviceModal';

setDeviceModalConfig({
  enableCache: true,        // 启用缓存
  cacheTime: 5 * 60 * 1000, // 缓存时间（毫秒）
  defaultTab: 'info',       // 默认标签页
  showDiagramTab: true,     // 显示系统图标签
  width: '1200px',          // 弹窗宽度
});
```

## Mock 数据

当API不可用时，组件会自动使用Mock数据：

### 设备信息Mock
```typescript
{
  id: 'device-001',
  name: '设备-device-001',
  type: '空调设备',
  model: 'AC-2000X',
  manufacturer: '格力电器',
  installDate: '2023-01-15',
  location: '1号楼-3层-东区',
  status: '运行中',
  power: 15.5,
  voltage: 380,
  current: 25.8,
}
```

### 数据查询Mock
自动生成24小时的模拟数据，包括功率、能耗、电压、电流等类型。

### 系统图Mock
使用占位图片：`https://via.placeholder.com/800x600/1f1f1f/ffffff?text=系统图示例`

## 样式定制

### CSS变量
```css
:root {
  --device-modal-bg: rgba(255, 255, 255, 0.05);
  --device-modal-border: rgba(255, 255, 255, 0.2);
  --device-modal-text: #fff;
  --device-modal-primary: #00a0e9;
}
```

### 自定义样式类
```vue
<style>
.device-detail-modal {
  /* 自定义样式 */
}
</style>
```

## 最佳实践

### 1. 错误处理
```typescript
try {
  showDeviceDetail('device-001');
} catch (error) {
  console.error('显示设备详情失败:', error);
}
```

### 2. 条件显示
```typescript
const handleShowDevice = (deviceId: string) => {
  if (!deviceId) {
    message.warning('请选择设备');
    return;
  }
  showDeviceDetail(deviceId);
};
```

### 3. 批量操作
```typescript
const showMultipleDevices = (deviceIds: string[]) => {
  deviceIds.forEach((id, index) => {
    setTimeout(() => {
      showDeviceDetail(id);
    }, index * 1000); // 间隔1秒显示
  });
};
```

## 注意事项

1. **设备ID必填**：调用时必须提供有效的设备ID
2. **内存管理**：弹窗会在关闭时自动销毁，避免内存泄漏
3. **并发限制**：同时只能显示一个设备详情弹窗
4. **网络处理**：API失败时会自动降级到Mock数据
5. **缓存策略**：相同设备的信息会被缓存，减少重复请求

## 故障排除

### 常见问题

1. **弹窗不显示**
   - 检查设备ID是否有效
   - 确认组件已正确注册

2. **数据不加载**
   - 检查API接口是否正常
   - 查看控制台错误信息

3. **样式异常**
   - 检查CSS样式是否被覆盖
   - 确认主题配置是否正确

### 调试方法
```typescript
// 开启调试模式
localStorage.setItem('device-modal-debug', 'true');

// 查看当前状态
import { deviceModalManager } from '@/utils/deviceModal';
console.log('当前设备ID:', deviceModalManager.getCurrentDeviceId());
console.log('弹窗是否可见:', deviceModalManager.isModalVisible());
```

## 更新日志

### v1.0.0
- 初始版本发布
- 支持设备信息、数据查询、系统图三个标签页
- 集成Mock数据支持
- 提供全局API调用方式
