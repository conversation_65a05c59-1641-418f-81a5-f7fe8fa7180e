<template>
  <PageCard :title="'能耗趋势'">
    <div class="qushi">
      <div class="search-box">
        <SearchForm :fields="searchFields" :initialValues="initialValues" />
      </div>

      <div class="chart-container">
        <common-chart :echart-obj="chartOption" />
      </div>
    </div>
  </PageCard>
</template>

<script setup lang="ts">
import PageCard from "@/components/PageCard.vue";
import SearchForm from "@/components/SearchForm.vue";
import CommonChart from "@/components/CommonChart.vue";
import { getEnergyTrend } from "@/api/energy";

import { ref, onMounted } from "vue";

const searchFields = ref([
  {
    type: "radio" as const,
    prop: "energyType",
    formItem: { label: "" },
    showRight: true,
    attrs: {
      size: "large",
    },
    options: [
      { label: "电", value: "electricity" },
      { label: "水", value: "water" },
    ],
    on: {
      change: (val: any) => {
        initialValues.value.energyType = val;
        getCurrentData();
      },
    },
  },
  {
    type: "radio" as const,
    prop: "timeModel",
    formItem: { label: "" },
    showRight: true,
    attrs: {
      size: "large",
    },
    options: [
      { label: "今日", value: "currentDay" },
      { label: "本月", value: "currentMonth" },
      { label: "本年", value: "currentYear" },
    ],
    on: {
      change: (val: any) => {
        initialValues.value.timeModel = val;
        getCurrentData();
      },
    },
  },
]);

const initialValues = ref({
  energyType: "electricity",
  timeModel: "currentDay",
});
const getCurrentData = async () => {
  try {
    const res = await getEnergyTrend(initialValues.value);
    if (res.code === 200) {
      chartOption.value.series[0].data = res.data.valueAxis; //数据
      chartOption.value.xAxis.data = res.data.xAxis;
    } else {
      console.log(res);
    }
  } catch (error) {
    console.error("加载能耗趋势失败:", error);
  }
};
// const dot_in_chart_base64 =
//   "image://data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAErSURBVHgBpZM9TsNAEIXfGiIiIUEq6tCDlCMkoubnEvRQUyAK6jgngCMABwBuQAp6p6aKkJBA/CzvxbPLKsHYEp802rE982zvvAXm8N73GUNG4X94YFwwuqiCDzvWWIdqOqHPhWYud4zeiwduXoHHD8Z7WbS5BOy2gZ2V+L4xY+CcmwaBIZejpy/g5BnQ+hsbGXC+Vq4kp8Cxs/8qdOdwWt2ciuTrwOrs1RhI61TZ7Vt9s1CNftE4kEBPWXKzFu2PsR8Fik80Jmwu6Wb4JxKYKNlabt6ksRpjCVzPBFpojDyRClwp22vH+f6JahJDnWU0wz2TkeaamKSyWTXGiL2TBSvrWp7QWMNktD/brfLTzUDRylHODlPe4DDl6WFaQNZmXNoRDhTW2J+v/wbWctvoQeOrKAAAAABJRU5ErkJggg==";
const chartOption = ref({
  backgroundColor: "transparent",
  grid: {
    left: "10%",
    right: "0%",
    top: "8%",
    bottom: "10%",
    // containLabel:true
  },
  xAxis: {
    type: "category",
    // boundaryGap: false,
    data: ["1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月"],
    axisLabel: {
      fontSize: "0.2rem",
      color: "#FFFFFF",
    },
    axisLine: {
      show: true,
      lineStyle: {
        color: "#FFFFFF",
      },
    },
    axisTick: {
      show: false,
    },
  },
  yAxis: {
    type: "value",
    name: "单位：kWh",
    nameGap: "0.3rem",
    nameTextStyle: {
      fontSize: "0.2rem",
      color: "#FFFFFF",
    },
    // max: 200,
    // step: 50,
    // min: 0,
    axisLabel: {
      fontSize: "0.2rem",
      color: "#FFFFFF",
    },
    axisLine: {
      show: true,
      lineStyle: {
        color: "#FFFFFF",
      },
    },
    axisTick: {
      show: false,
    },
    splitLine: {
      lineStyle: {
        color: "rgba(255, 255, 255, 0.3)",
        // type: "dashed",
      },
    },
  },
  tooltip: {
    trigger: "axis",
    textStyle: {
      fontSize: "0.2rem",
    },
  },
  series: [
    {
      data: [35, 36, 40, 50, 60, 80, 85, 80, 70, 60, 50, 40],
      type: "line",
      itemStyle: {
        color: "#00a0e9",
      },
      lineStyle: {
        width: 3,
        color: "#00a0e9",
      },
      symbolSize: 8,
    },
  ],
});
onMounted(() => {
  getCurrentData();
});
</script>

<style lang="less" scoped>
.search-box {
  height: 88px;
  // padding-top: 16px;
  display: flex;
  justify-content: flex-end;
  .name,
  .time {
    margin-right: 30px;
    :deep .ant-radio-button-wrapper {
      font-size: 18px;
    }
  }
}
.qushi {
  .chart-container {
    height: 670px;
  }
}
</style>
