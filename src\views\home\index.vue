<template>
  <div class="home ht100">
    <!-- <img src="@/assets/pages/0.png" alt=""> -->
    <page-layout >
      <!-- 左侧 -->
      <template v-slot:leftcontainer>
        <left-container />
      </template>
      <!-- 右侧 -->
      <template v-slot:rightcontainer>
        <!-- <right-container /> -->
      </template>
    </page-layout>
  </div>
</template>
<script lang="ts" setup>
import PageLayout from "@/components/PageLayout.vue";

import LeftContainer from "./components/LeftContainer.vue";
// import RightContainer from "./components/RightContainer.vue";
</script>

<style scoped lang="less">
.home {
}
</style>
