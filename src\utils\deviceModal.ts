import { createApp, App, ref } from "vue";
import DeviceDetailModal from "@/components/DeviceDetailModal.vue";
import Antd from "ant-design-vue";
import store from "@/store/index";

// 设备弹窗管理器类
class DeviceModalManager {
  private static instance: DeviceModalManager;
  private modalApp: App | null = null;
  private container: HTMLElement | null = null;
  private currentDeviceId = "";
  private isVisible = false;

  static getInstance(): DeviceModalManager {
    if (!DeviceModalManager.instance) {
      DeviceModalManager.instance = new DeviceModalManager();
    }
    return DeviceModalManager.instance;
  }

  // 显示设备详情弹窗
  show(deviceId: string, _deviceName?: string): void {
    if (!deviceId) {
      console.warn("Device ID is required");
      return;
    }

    this.currentDeviceId = deviceId;
    this.isVisible = true;

    // 如果已经有弹窗实例，直接更新
    if (this.modalApp && this.container) {
      this.updateModal();
      return;
    }

    // 创建新的弹窗实例
    this.createModal();
  }

  // 隐藏设备详情弹窗
  hide(): void {
    this.isVisible = false;
    this.updateModal();
  }

  // 销毁弹窗
  destroy(): void {
    if (this.modalApp) {
      this.modalApp.unmount();
      this.modalApp = null;
    }

    if (this.container && this.container.parentNode) {
      this.container.parentNode.removeChild(this.container);
      this.container = null;
    }

    this.currentDeviceId = "";
    this.isVisible = false;
  }

  // 创建弹窗实例
  private createModal(): void {
    // 创建容器元素
    this.container = document.createElement("div");
    this.container.id = "device-modal-container";
    document.body.appendChild(this.container);

    // 创建 Vue 应用实例
    this.modalApp = createApp({
      setup() {
        const visible = ref(false);
        const deviceId = ref("");

        const handleClose = () => {
          visible.value = false;
          // 延迟销毁，等待动画完成
          setTimeout(() => {
            DeviceModalManager.getInstance().destroy();
          }, 300);
        };

        const updateVisible = (value: boolean) => {
          visible.value = value;
          if (!value) {
            handleClose();
          }
        };

        // 暴露给外部更新的方法
        (window as any).__deviceModalUpdate = (newVisible: boolean, newDeviceId: string) => {
          console.log("%c 🎯 更新弹窗数据", "color:#e74c3c", { newVisible, newDeviceId });
          deviceId.value = newDeviceId;
          // 使用 nextTick 确保 deviceId 更新后再显示弹窗
          setTimeout(() => {
            visible.value = newVisible;
          }, 0);
        };

        return {
          visible,
          deviceId,
          handleClose,
          updateVisible,
        };
      },
      template: `
        <DeviceDetailModal
          v-model="visible"
          :deviceId="deviceId"
          @close="handleClose"
          @update:modelValue="updateVisible"
        />
      `,
      components: {
        DeviceDetailModal,
      },
    });

    // 使用 Ant Design Vue
    this.modalApp.use(Antd).use(store);

    // 挂载应用
    this.modalApp.mount(this.container);

    // 更新数据
    this.updateModal();
  }

  // 更新弹窗数据
  private updateModal(): void {
    if (this.modalApp && this.container) {
      // 使用全局方法更新数据
      if ((window as any).__deviceModalUpdate) {
        (window as any).__deviceModalUpdate(this.isVisible, this.currentDeviceId);
      }
    }
  }

  // 获取当前设备ID
  getCurrentDeviceId(): string {
    return this.currentDeviceId;
  }

  // 检查弹窗是否可见
  isModalVisible(): boolean {
    return this.isVisible;
  }
}

// 导出单例实例
export const deviceModalManager = DeviceModalManager.getInstance();

// 便捷方法
export const showDeviceDetail = (deviceId: string, deviceName?: string) => {
  deviceModalManager.show(deviceId, deviceName);
};

export const hideDeviceDetail = () => {
  deviceModalManager.hide();
};

export const destroyDeviceDetail = () => {
  deviceModalManager.destroy();
};

// Vue 3 插件形式
export default {
  install(app: App) {
    // 全局属性
    app.config.globalProperties.$showDeviceDetail = showDeviceDetail;
    app.config.globalProperties.$hideDeviceDetail = hideDeviceDetail;
    app.config.globalProperties.$destroyDeviceDetail = destroyDeviceDetail;

    // 全局方法
    app.provide("deviceModal", {
      show: showDeviceDetail,
      hide: hideDeviceDetail,
      destroy: destroyDeviceDetail,
      manager: deviceModalManager,
    });
  },
};

// TypeScript 声明扩展
declare module "@vue/runtime-core" {
  interface ComponentCustomProperties {
    $showDeviceDetail: (deviceId: string, deviceName?: string) => void;
    $hideDeviceDetail: () => void;
    $destroyDeviceDetail: () => void;
  }
}

// 使用示例和文档
export const USAGE_EXAMPLES = {
  // 基本使用
  basic: `
    import { showDeviceDetail } from '@/utils/deviceModal';
    
    // 显示设备详情
    showDeviceDetail('device-001');
  `,

  // 在组件中使用
  component: `
    <template>
      <a-button @click="handleShowDevice">查看设备详情</a-button>
    </template>
    
    <script setup>
    import { showDeviceDetail } from '@/utils/deviceModal';
    
    const handleShowDevice = () => {
      showDeviceDetail('device-001', '空调设备');
    };
    </script>
  `,

  // 使用全局属性
  global: `
    <template>
      <a-button @click="$showDeviceDetail('device-001')">查看设备详情</a-button>
    </template>
  `,

  // 使用 inject
  inject: `
    <script setup>
    import { inject } from 'vue';
    
    const deviceModal = inject('deviceModal');
    
    const handleShowDevice = () => {
      deviceModal.show('device-001');
    };
    </script>
  `,
};

// 配置选项
export interface DeviceModalConfig {
  // 是否启用缓存
  enableCache?: boolean;
  // 缓存时间（毫秒）
  cacheTime?: number;
  // 默认标签页
  defaultTab?: "info" | "data" | "diagram";
  // 是否显示系统图标签
  showDiagramTab?: boolean;
  // 自定义样式类名
  customClass?: string;
  // 弹窗宽度
  width?: string | number;
}

// 全局配置
let globalConfig: DeviceModalConfig = {
  enableCache: true,
  cacheTime: 5 * 60 * 1000, // 5分钟
  defaultTab: "info",
  showDiagramTab: true,
  width: "1200px",
};

// 设置全局配置
export const setDeviceModalConfig = (config: Partial<DeviceModalConfig>) => {
  globalConfig = { ...globalConfig, ...config };
};

// 获取全局配置
export const getDeviceModalConfig = (): DeviceModalConfig => {
  return { ...globalConfig };
};

// 重置全局配置
export const resetDeviceModalConfig = () => {
  globalConfig = {
    enableCache: true,
    cacheTime: 5 * 60 * 1000,
    defaultTab: "info",
    showDiagramTab: true,
    width: "1200px",
  };
};
