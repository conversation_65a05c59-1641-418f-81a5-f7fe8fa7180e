<template>
  <block-card title="楼宇运行概况">
    <div class="overview-container">
      <div class="overview-metrics">
        <div class="metric-row">
          <div class="metric-item energy">
            <div class="metric-icon">
              <!-- <img src="/icon/electricity_consumption.svg" alt="能耗" /> -->
            </div>
            <div class="metric-info">
              <div class="metric-value">
                {{ overviewData.energy.value }}
                <span class="unit">{{ overviewData.energy.unit }}</span>
              </div>
              <div class="metric-label">今日能耗</div>
              <div class="metric-trend" :class="overviewData.energy.trend">
                {{ overviewData.energy.change }}
              </div>
            </div>
          </div>

          <div class="metric-item temperature">
            <div class="metric-icon">
              <span class="temp-icon">🌡</span>
            </div>
            <div class="metric-info">
              <div class="metric-value">
                {{ overviewData.temperature.value }}
                <span class="unit">{{ overviewData.temperature.unit }}</span>
              </div>
              <div class="metric-label">平均温度</div>
              <div class="metric-trend" :class="overviewData.temperature.trend">
                {{ overviewData.temperature.change }}
              </div>
            </div>
          </div>
        </div>

        <div class="metric-row">
          <div class="metric-item occupancy">
            <div class="metric-icon">
              <span class="occupancy-icon">👥</span>
            </div>
            <div class="metric-info">
              <div class="metric-value">
                {{ overviewData.occupancy.value }}
                <span class="unit">{{ overviewData.occupancy.unit }}</span>
              </div>
              <div class="metric-label">入驻率</div>
              <div class="metric-trend" :class="overviewData.occupancy.trend">
                {{ overviewData.occupancy.change }}
              </div>
            </div>
          </div>

          <div class="metric-item efficiency">
            <div class="metric-icon">
              <span class="efficiency-icon">⚡</span>
            </div>
            <div class="metric-info">
              <div class="metric-value">
                {{ overviewData.efficiency.value }}
                <span class="unit">{{ overviewData.efficiency.unit }}</span>
              </div>
              <div class="metric-label">运行效率</div>
              <div class="metric-trend" :class="overviewData.efficiency.trend">
                {{ overviewData.efficiency.change }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="overview-chart">
        <div class="chart-header">
          <span class="chart-title">设备运行状态分布</span>
          <a-radio-group v-model:value="chartType" size="small">
            <a-radio-button value="pie">饼图</a-radio-button>
            <a-radio-button value="bar">柱图</a-radio-button>
          </a-radio-group>
        </div>
        <div class="chart-content">
          <common-chart :echart-obj="chartOption" />
        </div>
      </div>
    </div>
  </block-card>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import BlockCard from "@/components/BlockCard.vue";
import CommonChart from "@/components/CommonChart.vue";

const chartType = ref("pie");

const overviewData = ref({
  energy: {
    value: 1248,
    unit: "kWh",
    change: "↓ 5.2%",
    trend: "down",
  },
  temperature: {
    value: 24.5,
    unit: "°C",
    change: "↑ 1.2°C",
    trend: "up",
  },
  occupancy: {
    value: 85,
    unit: "%",
    change: "↑ 3.5%",
    trend: "up",
  },
  efficiency: {
    value: 92,
    unit: "%",
    change: "↑ 2.1%",
    trend: "up",
  },
});

const equipmentStatusData = [
  { name: "正常运行", value: 156, color: "#52c41a" },
  { name: "维护中", value: 12, color: "#ffc107" },
  { name: "故障", value: 8, color: "#ff4d4f" },
  { name: "离线", value: 4, color: "#8c8c8c" },
];

const chartOption = computed(() => {
  if (chartType.value === "pie") {
    return {
      tooltip: {
        trigger: "item",
        backgroundColor: "rgba(0, 26, 40, 0.8)",
        borderColor: "#4eedff",
        textStyle: { color: "#ffffff" },
      },
      legend: {
        orient: "vertical",
        right: "10%",
        top: "center",
        textStyle: { color: "#ffffff", fontSize: "0.14rem" },
      },
      series: [
        {
          type: "pie",
          radius: ["40%", "70%"],
          center: ["35%", "50%"],
          data: equipmentStatusData.map((item) => ({
            name: item.name,
            value: item.value,
            itemStyle: { color: item.color },
          })),
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: "rgba(0, 0, 0, 0.5)",
            },
          },
        },
      ],
    };
  } else {
    return {
      tooltip: {
        trigger: "axis",
        backgroundColor: "rgba(0, 26, 40, 0.8)",
        borderColor: "#4eedff",
        textStyle: { color: "#ffffff" },
      },
      grid: {
        left: "3%",
        right: "4%",
        bottom: "3%",
        top: "10%",
        containLabel: true,
      },
      xAxis: {
        type: "category",
        data: equipmentStatusData.map((item) => item.name),
        axisLine: { lineStyle: { color: "#4eedff" } },
        axisLabel: { color: "#ffffff", fontSize: "0.12rem" },
      },
      yAxis: {
        type: "value",
        axisLine: { lineStyle: { color: "#4eedff" } },
        axisLabel: { color: "#ffffff" },
        splitLine: { lineStyle: { color: "rgba(78, 237, 255, 0.2)" } },
      },
      series: [
        {
          type: "bar",
          data: equipmentStatusData.map((item) => ({
            value: item.value,
            itemStyle: { color: item.color },
          })),
          barWidth: "60%",
        },
      ],
    };
  }
});
</script>

<style scoped lang="less">
.overview-container {
  padding: 30px;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 30px;

  .overview-metrics {
    display: flex;
    flex-direction: column;
    gap: 20px;

    .metric-row {
      display: flex;
      gap: 20px;

      .metric-item {
        flex: 1;
        display: flex;
        align-items: center;
        gap: 15px;
        padding: 20px;
        border-radius: 12px;
        border: 2px solid rgba(78, 237, 255, 0.2);
        background: rgba(0, 26, 40, 0.3);

        .metric-icon {
          width: 60px;
          height: 60px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 50%;
          background: rgba(78, 237, 255, 0.1);

          img {
            width: 35px;
            height: 35px;
            filter: brightness(1.2);
          }

          span {
            font-size: 28px;
          }
        }

        .metric-info {
          flex: 1;

          .metric-value {
            font-family: "HarmonyOS Sans SC";
            font-size: 32px;
            font-weight: 700;
            color: #4eedff;
            line-height: 1;
            margin-bottom: 5px;

            .unit {
              font-size: 20px;
              margin-left: 3px;
              opacity: 0.8;
            }
          }

          .metric-label {
            font-family: "HarmonyOS Sans SC";
            font-size: 22px;
            color: #ffffff;
            margin-bottom: 5px;
          }

          .metric-trend {
            font-family: "HarmonyOS Sans SC";
            font-size: 18px;
            font-weight: 500;

            &.up {
              color: #52c41a;
            }

            &.down {
              color: #ff4d4f;
            }
          }
        }
      }
    }
  }

  .overview-chart {
    flex: 1;
    display: flex;
    flex-direction: column;

    .chart-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;

      .chart-title {
        font-family: "HarmonyOS Sans SC";
        font-size: 28px;
        color: #4eedff;
        font-weight: 600;
      }
    }

    .chart-content {
      flex: 1;
      min-height: 300px;
    }
  }
}
</style>
