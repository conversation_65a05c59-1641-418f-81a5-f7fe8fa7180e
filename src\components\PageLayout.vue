<template>
  <div class="flex page-container ht100">
    <transition name="left-container">
      <div class="left-container flex flex-column ht100" v-if="pageShow">
        <slot name="leftcontainer" />
      </div>
    </transition>
    <transition name="right-container">
      <div class="right-container flex flex-column ht100" v-if="pageShow">
        <slot name="rightcontainer" />
      </div>
    </transition>

    <!-- 建筑楼层选择器 -->
    <transition name="fade" appear>
      <BuildingFloorSelector />
    </transition>
    <!-- tree -->
    <!-- 是否显示树结构 -->
    <transition name="fade" v-if="delayedShowTree">
      <div class="tree-container">
        <slot name="treecontainer" />
      </div>
    </transition>
  </div>
</template>

<script lang="ts" setup>
import { inject, computed, ref, watch, onMounted } from "vue";
import BuildingFloorSelector from "@/components/BuildingFloorSelector.vue";

const props = defineProps({
  showTree: {
    type: Boolean,
    default: false,
  },
});

const ips = inject("pageShow", ref<boolean>(true));
const pageShow = computed(() => ips.value);

// 延迟显示控制 - 初始为 false
const delayedShowTree = ref(false);

// 延迟函数
const timeout = (time: number) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(true);
    }, time);
  });
};

// 监听 props.showTree 变化，延迟1秒后更新 delayedShowTree
watch(
  () => props.showTree,
  async (newValue) => {
    // console.log("%c 🌳 PageLayout showTree 状态变化", "color:#9b59b6", {
    //   newValue,
    //   oldValue: delayedShowTree.value,
    // });

    if (newValue) {
      // 如果 showTree 为 true，延迟1秒后显示
      console.log("%c ⏰ Tree 开始延迟1秒...", "color:#f39c12");
      await timeout(1000);
      console.log("%c ✅ Tree 延迟结束，显示组件", "color:#27ae60");
      delayedShowTree.value = newValue;
    } else {
      // 如果 showTree 为 false，立即隐藏
      // console.log("%c ❌ Tree 立即隐藏组件", "color:#95a5a6");
      delayedShowTree.value = newValue;
    }
  },
  { immediate: true },
);

// 组件挂载时也检查一次
onMounted(async () => {
  if (props.showTree) {
    await timeout(1000);
    delayedShowTree.value = props.showTree;
  }
});
</script>

<style scoped lang="less">
.page-container {
  justify-content: space-between;
  position: relative;
  img {
    height: 1290px;
    width: 5120px;
    position: absolute;
    z-index: -1;
  }
  .left-container,
  .right-container {
    width: 1300px;
    pointer-events: auto;
    height: 100%;
    display: flex;
    flex-flow: column;
    justify-content: space-between;
    align-items: center;
  }
  .left-container {
    padding: 24px 0 24px 16px;
  }
  .right-container {
    padding: 24px 16px 24px 0px;
  }
  .tree-container {
    pointer-events: auto;
    position: absolute;
    top: 24px;
    left: 1350px;
    max-height: 600px;
    background: rgba(#001a28, 0.7);
    padding: 24px 10px;
    border: 3px solid;
    border-image: linear-gradient(0deg, #0278faff, #0278fa00) 1;
    overflow: auto;

    // min-width: 500px;
  }
}
</style>
