import { onBeforeRouteLeave } from "vue-router";

/**
 * 路由离开前的通用处理逻辑
 * @param {Function} handleLeave - 自定义处理函数，返回布尔值表示是否允许离开
 * @param {String} confirmMessage - 确认提示信息
 */
export function useRouteLeaveGuard(handleLeave: (to: any, from: any) => boolean, confirmMessage = "有未保存的内容，确定要离开吗？") {
  onBeforeRouteLeave((to, from, next) => {
    // 执行通用前置逻辑
    console.log("执行通用路由离开处理...");

    // 调用组件自定义的处理逻辑
    const canLeave = handleLeave ? handleLeave(to, from) : true;

    if (canLeave) {
      next(); // 允许离开
    } else {
      // 通用确认逻辑
      if (window.confirm(confirmMessage)) {
        next();
      } else {
        next(false); // 阻止导航
      }
    }
  });
}
