<template>
  <PageCard :title="'消防事件'">
    <!-- 表格 -->
    <div class="table-container">
      <BaseTable
        class="ant-table-striped"
        :columns="columns"
        :data-source="data"
        :row-class-name="(_record:any, index:number) => (index % 2 === 1 ? 'table-dan' : 'table-shuang')"
        :pagination="{
          total: total,
          current: pageNum,
          pageSize: pageSize,
          onChange: handlePageChange,
        }"
        :bordered="false"
      >
        <template v-slot:column="scoped">
          <a-row
            v-if="scoped.column.dataIndex === 'operation'"
            class="operation"
            align="middle"
            justify="center"
          >
            <img src="@/assets/pages-icon/energy/search1.png" alt="" srcset="" />
            <img src="@/assets/pages-icon/energy/place.png" alt="" srcset="" />
          </a-row>
        </template>
      </BaseTable>
    </div>
  </PageCard>
</template>
<script lang="ts" setup>
import PageCard from "@/components/PageCard.vue";
import BaseTable from "@/components/BaseTable.vue";
import { ref } from "vue";
import { getDeviceStatusList } from "@/api/energy";

const columns = [
  { title: "报警时间", dataIndex: "alarmTime" },
  { title: "设备名称", dataIndex: "deviceName" },
  { title: "报警详情", dataIndex: "alarmDetail" },
  { title: "操作", dataIndex: "operation" },
];

// 获取列表数据
const data = ref<[]>([]);
// 添加分页相关响应式变量
const pageNum = ref(1);
const pageSize = ref(20);
const total = ref(0); // 总条数

// 修改获取列表数据的方法
const getStatusList = async () => {
  try {
    const params = {
      pageNum: pageNum.value, // 使用当前页码
      pageSize: pageSize.value, // 使用当前页大小
    };
    const res = await getDeviceStatusList(params);
    if (res.code === 200) {
      res.rows.forEach((item: any) => {
        switch (item.status) {
          case 1:
            item.statusText = "未激活";
            break;
          case 4:
            item.statusText = "离线";
            break;
        }
      });
      data.value = res.rows;
      total.value = res.total; // 从接口响应中获取总条数
    } else {
      console.log(res);
    }
  } catch (error) {
    console.error("加载设备概览失败:", error);
  }
};
// 分页变化处理函数
const handlePageChange = (newPage: number, newPageSize: number) => {
  pageNum.value = newPage; // 更新当前页码
  pageSize.value = newPageSize; // 更新当前页码
  // getStatusList(); // 重新请求数据
};
getStatusList();
</script>
<style scoped lang="less">
.page-card {
  width: 704px;
  .table-container {
    position: relative;
    height: 100%;
    overflow: hidden;
  }
  :deep .ant-table-striped {
    position: relative;
    left: 10px;
    .table-dan {
      height: 57px;
      margin: 8px;
      td {
        background-color: rgba(7, 27, 51, 1);
        color: #fff;
      }
    }
    .table-shuang {
      height: 57px;
      margin: 8px;
      td {
        background-color: rgba(15, 36, 61, 1);
        color: #fff;
      }
    }
    .ant-table-thead {
      th {
        background-color: rgba(15, 41, 82, 1);
        color: #fff;
        height: 67px;
      }
    }
  }

  .ant-table-striped :deep(.ant-table-body) {
    overflow-y: auto !important;
    max-height: 590px !important;
  }

  .ant-table-striped :deep(.ant-table-body::-webkit-scrollbar) {
    position: absolute;
    left: 10px;
    width: 3px;
    height: 8px;
  }
  img {
    margin-right: 20px;
    cursor: pointer;
  }
  img:hover {
    opacity: 0.7;
  }
}
</style>
