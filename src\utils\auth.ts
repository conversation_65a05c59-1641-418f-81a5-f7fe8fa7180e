import { getUserInfo } from "@/api/auth";
import store from "@/store";

// 初始化用户信息
export const initUserInfo = async () => {
  const token = localStorage.getItem("token");
  if (token && !store.state.userInfo) {
    try {
      const response = await getUserInfo();
      store.commit("setUserInfo", response.data);
      store.commit("setLoginStatus", true);
      return response.data;
    } catch (error) {
      // token无效，清除登录状态
      store.commit("logout");
      throw error;
    }
  }
  return store.state.userInfo;
};

// 检查是否有权限
export const hasPermission = (permission: string): boolean => {
  const userInfo = store.state.userInfo;
  if (!userInfo) return false;

  // 管理员拥有所有权限
  if (userInfo.permissions.includes("*")) return true;

  return userInfo.permissions.includes(permission);
};
