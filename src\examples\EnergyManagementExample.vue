<template>
  <div class="energy-management-example">
    <h2>本页内容只做示例</h2>
    <!-- 
    <div class="overview-section">
      <h3>设备概览</h3>
      <a-spin :spinning="loading">
        <div class="overview-cards" v-if="deviceOverview">
          <div class="card">
            <div class="card-title">总设备数</div>
            <div class="card-value">{{ deviceOverview.total }}</div>
          </div>
          <div class="card">
            <div class="card-title">在线设备</div>
            <div class="card-value online">{{ deviceOverview.online }}</div>
          </div>
          <div class="card">
            <div class="card-title">离线设备</div>
            <div class="card-value offline">{{ deviceOverview.offline }}</div>
          </div>
          <div class="card">
            <div class="card-title">故障设备</div>
            <div class="card-value fault">{{ deviceOverview.fault }}</div>
          </div>
        </div>
      </a-spin>

      <a-button @click="refreshOverview" :loading="loading">刷新概览</a-button>
    </div>


    <div class="status-list-section">
      <h3>设备状态列表</h3>
      <div class="filters">
        <a-input
          v-model:value="searchKeyword"
          placeholder="搜索设备名称"
          style="width: 200px; margin-right: 10px"
          @pressEnter="searchDevice"
        />
        <a-select
          v-model:value="selectedStatus"
          placeholder="选择状态"
          style="width: 120px; margin-right: 10px"
          @change="filterByStatus"
        >
          <a-select-option value="">全部</a-select-option>
          <a-select-option value="normal">正常</a-select-option>
          <a-select-option value="warning">警告</a-select-option>
          <a-select-option value="error">错误</a-select-option>
          <a-select-option value="offline">离线</a-select-option>
        </a-select>
        <a-button @click="searchDevice" type="primary">搜索</a-button>
      </div>
      <a-table
        :dataSource="deviceStatusList"
        :columns="columns"
        :loading="loading"
        :pagination="pagination"
        @change="handleTableChange"
        rowKey="id"
      >
        <template #status="{ record }">
          <a-tag :color="getStatusColor(record.status)">
            {{ getStatusText(record.status) }}
          </a-tag>
        </template>

        <template #alarmLevel="{ record }">
          <a-tag v-if="record.alarmLevel" :color="getAlarmColor(record.alarmLevel)">
            {{ getAlarmText(record.alarmLevel) }}
          </a-tag>
        </template>
      </a-table>
    </div>-->
  </div>
</template>

<!-- <script setup lang="ts">
import { ref, onMounted, computed } from "vue";
import { message } from "ant-design-vue";
import { getDeviceOverview, getDeviceStatusList } from "@/api/energy";
import type {
  DeviceOverviewResponse,
  DeviceStatusItem,
  DeviceStatusListParams,
} from "@/models/energy";

// 响应式数据
const loading = ref(false);
const deviceOverview = ref<DeviceOverviewResponse | null>(null);
const deviceStatusList = ref<DeviceStatusItem[]>([]);
const searchKeyword = ref("");
const selectedStatus = ref("");

// 分页配置
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`,
});

// 表格列配置
const columns = [
  {
    title: "设备名称",
    dataIndex: "deviceName",
    key: "deviceName",
  },
  {
    title: "设备类型",
    dataIndex: "deviceType",
    key: "deviceType",
  },
  {
    title: "位置",
    dataIndex: "location",
    key: "location",
  },
  {
    title: "状态",
    dataIndex: "status",
    key: "status",
    slots: { customRender: "status" },
  },
  {
    title: "告警级别",
    dataIndex: "alarmLevel",
    key: "alarmLevel",
    slots: { customRender: "alarmLevel" },
  },
  {
    title: "最后检查时间",
    dataIndex: "lastCheckTime",
    key: "lastCheckTime",
  },
];

// 加载设备概览
const loadDeviceOverview = async () => {
  try {
    loading.value = true;
    const response = await getDeviceOverview();

    if (response.success) {
      deviceOverview.value = response.data;
    } else {
      message.error(response.message || "获取设备概览失败");
    }
  } catch (error) {
    console.error("获取设备概览失败:", error);
    message.error("获取设备概览失败");
  } finally {
    loading.value = false;
  }
};

// 加载设备状态列表
const loadDeviceStatusList = async (params?: DeviceStatusListParams) => {
  try {
    loading.value = true;
    const requestParams = {
      page: pagination.value.current,
      pageSize: pagination.value.pageSize,
      keyword: searchKeyword.value,
      status: selectedStatus.value,
      ...params,
    };

    const response = await getDeviceStatusList(requestParams);

    if (response.success) {
      deviceStatusList.value = response.data.deviceList;
      pagination.value.total = response.data.total;
      pagination.value.current = response.data.currentPage;
    } else {
      message.error(response.message || "获取设备状态列表失败");
    }
  } catch (error) {
    console.error("获取设备状态列表失败:", error);
    message.error("获取设备状态列表失败");
  } finally {
    loading.value = false;
  }
};

// 刷新概览
const refreshOverview = () => {
  loadDeviceOverview();
};

// 搜索设备
const searchDevice = () => {
  pagination.value.current = 1;
  loadDeviceStatusList();
};

// 按状态筛选
const filterByStatus = () => {
  pagination.value.current = 1;
  loadDeviceStatusList();
};

// 表格变化处理
const handleTableChange = (pag: any) => {
  pagination.value.current = pag.current;
  pagination.value.pageSize = pag.pageSize;
  loadDeviceStatusList();
};

// 获取状态颜色
const getStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    normal: "green",
    warning: "orange",
    error: "red",
    offline: "gray",
  };
  return colorMap[status] || "default";
};

// 获取状态文本
const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    normal: "正常",
    warning: "警告",
    error: "错误",
    offline: "离线",
  };
  return textMap[status] || status;
};

// 获取告警级别颜色
const getAlarmColor = (level: string) => {
  const colorMap: Record<string, string> = {
    low: "blue",
    medium: "orange",
    high: "red",
    critical: "purple",
  };
  return colorMap[level] || "default";
};

// 获取告警级别文本
const getAlarmText = (level: string) => {
  const textMap: Record<string, string> = {
    low: "低",
    medium: "中",
    high: "高",
    critical: "严重",
  };
  return textMap[level] || level;
};

// 组件挂载时加载数据
onMounted(() => {
  loadDeviceOverview();
  loadDeviceStatusList();
});
</script> -->

<style scoped>
.energy-management-example {
  padding: 20px;
}

.overview-section {
  margin-bottom: 30px;
}

.overview-cards {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.card {
  background: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 20px;
  text-align: center;
  min-width: 120px;
}

.card-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 10px;
}

.card-value {
  font-size: 24px;
  font-weight: bold;
  color: #333;
}

.card-value.online {
  color: #52c41a;
}

.card-value.offline {
  color: #999;
}

.card-value.fault {
  color: #ff4d4f;
}

.filters {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
}

.status-list-section h3 {
  margin-bottom: 20px;
}
</style>
