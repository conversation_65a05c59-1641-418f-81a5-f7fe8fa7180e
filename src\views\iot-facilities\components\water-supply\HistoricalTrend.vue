<template>
  <PageCard :title="'历史趋势'">
    <div class="historical-trend">
      <div class="search-box">
        <a-radio-group v-model:value="timeRange" button-style="solid" size="large" class="time">
          <a-radio-button value="day">今日</a-radio-button>
          <a-radio-button value="month">本月</a-radio-button>
          <a-radio-button value="year">本年</a-radio-button>
        </a-radio-group>
      </div>
      <div class="chart-container">
        <common-chart :echart-obj="chartOption" />
      </div>
    </div>
  </PageCard>
</template>

<script lang="ts" setup>
import PageCard from "@/components/PageCard.vue";
import CommonChart from "@/components/CommonChart.vue";
import { ref } from "vue";
const timeRange = ref("year");

const chartOption = ref({
  backgroundColor: "transparent",
  grid: {
    left: "3%",
    right: "4%",
    bottom: "14%",
    top: "20%",
    containLabel: true,
  },
  // 图例配置
  legend: {
    data: ["系列1", "系列2", "系列3"], // 图例数据，从API获取
    icon: "roundRect",
    bottom: "0%", // 图例位置
    textStyle: {
      fontSize: "0.16rem",
      color: "#fff",
    },
  },
  tooltip: {
    trigger: "axis",
    // textStyle: {
    //   fontSize: "0.2rem",
    //   color: "#fff",
    // },
    // backgroundColor: "rgba(0, 10, 20, 0.8)",
    // borderColor: "rgba(255, 255, 255, 0.2)",
  },
  xAxis: {
    type: "category",
    data: ["1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月"],
    axisLine: {
      lineStyle: {
        color: "rgba(255, 255, 255, 0.3)",
      },
    },
    axisTick: {
      show: false,
    },
    axisLabel: {
      color: "#fff",
      fontSize: "0.2rem",
    },
    // splitLine: {
    //   show: true,
    //   lineStyle: {
    //     color: "rgba(255, 255, 255, 0.1)",
    //     type: "dashed",
    //   },
    // },
  },
  yAxis: {
    type: "value",
    name: "单位：个",
    nameGap: 30,
    nameTextStyle: {
      fontSize: "0.2rem",
      color: "#FFFFFF",
    },
    // min: 0,
    // max: 140,
    // interval: 20,
    axisLine: {
      show: true,
      lineStyle: {
        color: "rgba(255, 255, 255, 0.3)",
      },
    },
    axisTick: {
      show: false,
    },
    axisLabel: {
      color: "#fff",
      fontSize: "0.2rem",
    },
    splitLine: {
      lineStyle: {
        color: "rgba(255, 255, 255, 0.3)",
        // type: "dashed",
      },
    },
  },
  series: [
    {
      name: "系列1",
      type: "line",
      // smooth: true,
      // symbol: "circle",
      // symbolSize: 8,
      // itemStyle: {
      //   color: "#00a0e9",
      // },
      // lineStyle: {
      //   width: 3,
      //   color: "#00a0e9",
      // },
      data: [8, 22, 28, 28, 30, 36, 50, 62, 68, 84, 74, 38],
    },
    {
      name: "系列2",
      type: "line",
      // smooth: true,
      // symbol: "circle",
      // symbolSize: 8,
      // itemStyle: {
      //   color: "#ff9f00",
      // },
      // lineStyle: {
      //   width: 3,
      //   color: "#ff9f00",
      // },
      data: [105, 92, 84, 85, 85, 78, 62, 50, 45, 30, 38, 75],
    },
    {
      name: "系列3",
      type: "line",
      // smooth: true,
      // symbol: "circle",
      // symbolSize: 8,
      // itemStyle: {
      //   color: "#ff9f00",
      // },
      // lineStyle: {
      //   width: 3,
      //   color: "#ff9f00",
      // },
      data: [11, 22, 33, 44, 44, 55, 55, 55, 44, 33, 89, 63],
    },
  ],
});
</script>

<style lang="less" scoped>
.page-card {
  height: 574px;
  color: #fff;
}
.chart-container {
  height: 430px;
}
.search-box {
  height: 88px;
  padding-top: 16px;
  display: flex;
  justify-content: flex-end;
  > div {
    margin-right: 30px;
  }
}
</style>
