<template>
  <div class="building-floor-selector-container" v-if="showBuilding || showFloor">
    <div class="building-selector" v-if="showBuilding">
      <ul class="dong">
        <li
          v-for="building in buildings"
          :key="building.id"
          :class="[building.buildId === selectedBuilding ? 'active' : '']"
          @click="onBuildingChange(building)"
        >
          {{ building.name }}
        </li>
      </ul>
    </div>
    <div class="floor-selector" v-if="showFloor">
      <ul class="dong">
        <li
          v-for="floor in floors"
          :key="floor.id"
          :class="[floor.id === selectedFloor ? 'active' : '']"
          @click="onFloorChange(floor.id)"
        >
          {{ floor.name }}
        </li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onBeforeUnmount } from "vue";
import BuildingMapper from "@/utils/buildingMapper";

import { useRouter } from "vue-router";
const router = useRouter();
const currentRoute = router.currentRoute;

// 原始的显示状态（从路由 meta 获取）
const originalShowBuilding = computed(() => {
  const routeShowBuilding = currentRoute.value.meta?.showBuilding;
  const routeShowFloor = currentRoute.value.meta?.showFloor;
  // 如果showFloor为true，则showBuilding必定为true
  return routeShowFloor || routeShowBuilding || false;
});

const originalShowFloor = computed(() => {
  return currentRoute.value.meta?.showFloor || false;
});

// 延迟显示控制 - 初始为 false
const showBuilding = ref(false);
const showFloor = ref(false);

// 延迟函数
const timeout = (time: number) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(true);
    }, time);
  });
};

// 监听原始显示状态变化，延迟1秒后更新实际显示状态
watch(
  [originalShowBuilding, originalShowFloor],
  async ([newShowBuilding, newShowFloor]) => {
    // console.log("%c 🏢 BuildingFloorSelector 状态变化", "color:#e74c3c", {
    //   newShowBuilding,
    //   newShowFloor,
    //   route: currentRoute.value.name,
    // });
    if (newShowBuilding || newShowFloor) {
      // 如果需要显示，延迟2秒后显示
      await timeout(2000);
      showBuilding.value = Boolean(newShowBuilding);
      showFloor.value = Boolean(newShowFloor);
    } else {
      // 如果不需要显示，立即隐藏
      showBuilding.value = Boolean(newShowBuilding);
      showFloor.value = Boolean(newShowFloor);
    }
  },
  { immediate: true },
);

// 组件挂载时也检查一次
onMounted(async () => {
  if (originalShowBuilding.value || originalShowFloor.value) {
    await timeout(2000);
    showBuilding.value = Boolean(originalShowBuilding.value);
    showFloor.value = Boolean(originalShowFloor.value);
  }
});
// 组件卸载前
onBeforeUnmount(() => {
  showBuilding.value = false;
  showFloor.value = false;
});

const buildings = ref(BuildingMapper.getAllBuildings());
const selectedBuilding = ref("park");
const floors = ref([] as any[]);
const selectedFloor = ref("");
const ue5 = window.ue5;
const onBuildingChange = (build: any) => {
  if (selectedBuilding.value !== build.buildId) {
    selectedFloor.value = "";
  }
  selectedBuilding.value = build.buildId;
  floors.value = BuildingMapper.getFloorsByBuildingId(build.id);
  // 不默认选中
  // selectedFloor.value = floors.value.length > 0 ? floors.value[0].id : "";
  console.log("%c Line:63 🍌 切换建筑传参：", "color:#465975", { Building: build.buildId });
  ue5("SwitchBuilding", { Building: build.buildId });
  // emitChange();
};

const onFloorChange = (value: string) => {
  console.log("%c Line:70 🍏切换楼层传参：", "color:#e41a6a", {
    Building: selectedBuilding.value,
    [selectedBuilding.value === "PowerStation" ? "PowerStation" : "Floor"]: value,
  });
  selectedFloor.value = value;
  ue5("SwitchFloor", {
    Building: selectedBuilding.value,
    [selectedBuilding.value === "PowerStation" ? "PowerStation" : "Floor"]: value,
  });
  // emitChange();
};

// const emitChange = () => {
//   console.log("pppppp");
// };

// onMounted(() => {
//   if (selectedBuilding.value) {
//     floors.value = BuildingMapper.getFloorsByBuildingId(selectedBuilding.value);
//   }
// });

// watch(
//   () => props.defaultBuildingId,
//   (newVal) => {
//     if (newVal && newVal !== selectedBuilding.value) {
//       selectedBuilding.value = newVal;
//       floors.value = BuildingMapper.getFloorsByBuildingId(newVal);
//     }
//   },
// );

// watch(
//   () => props.defaultFloorId,
//   (newVal) => {
//     if (newVal && newVal !== selectedFloor.value) {
//       selectedFloor.value = newVal;
//     }
//   },
// );
</script>

<style scoped lang="less">
/* 建筑楼层选择器样式 */
.building-floor-selector-container {
  position: absolute;
  left: 1350px;
  bottom: 24px;
  z-index: 10;
  display: flex;
  align-items: flex-end;
}

.building-selector,
.floor-selector {
  margin: 0 5px;
}

.dong {
  pointer-events: auto;
  li {
    color: #fff;
    font-size: 16px;
    margin-bottom: 5px;
    padding: 8px 16px;
    text-align: center;
    background: #e8e8e84d;
    cursor: pointer;
    &:hover {
      background: rgba(0, 0, 0, 0.3);
    }
    &.active {
      background: #00aabd;
    }
  }
}
</style>
