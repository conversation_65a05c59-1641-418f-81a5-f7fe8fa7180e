// UE通信工具类
// 由于是UE5插件webUI，所以当前文件用不上
type CallbackFunction = (params?: any) => void;

class UECommunication {
  private listeners: Map<string, CallbackFunction[]> = new Map();

  /**
   * 网页发送消息给UE
   * @param functionName 函数名（必传）
   * @param params 参数对象（可选）
   */
  sendToUE(functionName: string, params?: Record<string, any>): void {
    if (!functionName || typeof functionName !== "string") {
      console.error("UE通信错误: 函数名必须是非空字符串");
      return;
    }

    try {
      const message = {
        type: functionName,
        [functionName]: params || "",
      };

      // 使用全局的uee方法发送消息
      if (window.uee && typeof window.uee === "function") {
        window.uee(message);
        console.log("发送消息给UE:", message);
      } else {
        console.warn("UE接口未准备就绪");
      }
    } catch (error) {
      console.error("发送消息给UE失败:", error);
    }
  }

  /**
   * 监听来自UE的消息
   * @param functionName 函数名（必传）
   * @param callback 回调函数
   */
  onUEMessage(functionName: string, callback: CallbackFunction): void {
    if (!functionName || typeof functionName !== "string") {
      console.error("UE通信错误: 函数名必须是非空字符串");
      return;
    }

    if (typeof callback !== "function") {
      console.error("UE通信错误: 回调函数必须是函数类型");
      return;
    }

    // 添加监听器
    if (!this.listeners.has(functionName)) {
      this.listeners.set(functionName, []);
    }
    this.listeners.get(functionName)!.push(callback);
  }

  /**
   * 移除UE消息监听
   * @param functionName 函数名
   * @param callback 要移除的回调函数（可选，不传则移除所有）
   */
  offUEMessage(functionName: string, callback?: CallbackFunction): void {
    if (!this.listeners.has(functionName)) return;

    if (callback) {
      const callbacks = this.listeners.get(functionName)!;
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    } else {
      this.listeners.delete(functionName);
    }
  }

  /**
   * 初始化全局监听器
   */
  initGlobalListener(): void {
    // 监听来自UE的消息
    if (window.addResponseEventListener && typeof window.addResponseEventListener === "function") {
      window.addResponseEventListener("*", (message: string) => {
        try {
          const data = JSON.parse(message);
          const { type, ...params } = data;

          if (type && this.listeners.has(type)) {
            const callbacks = this.listeners.get(type)!;
            callbacks.forEach((callback) => {
              try {
                callback(params);
              } catch (error) {
                console.error(`执行UE消息回调失败 [${type}]:`, error);
              }
            });
          }
        } catch (error) {
          console.error("解析UE消息失败:", error);
        }
      });
    }
  }

  /**
   * 销毁所有监听器
   */
  destroy(): void {
    this.listeners.clear();
  }
}

// 创建单例实例
const ueComm = new UECommunication();

// 导出实例和类型
export default ueComm;
export type { UECommunication, CallbackFunction };
