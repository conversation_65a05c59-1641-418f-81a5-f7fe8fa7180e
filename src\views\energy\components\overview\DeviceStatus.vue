<template>
  <PageCard :title="'设备状态'">
    <SearchForm :fields="searchFields" :initialValues="initialValues" />
    <div class="table-container">
      <BaseTable
        class="ant-table-striped"
        :columns="columns"
        :data-source="data"
        :row-class-name="(_record:any, index:number) => (index % 2 === 1 ? 'table-dan' : 'table-shuang')"
        :pagination="{
          total: total,
          current: pageNum,
          pageSize: pageSize,
          onChange: handlePageChange,
        }"
        :bordered="false"
      >
        <template v-slot:column="scoped">
          <a-row
            v-if="scoped.column.dataIndex === 'operation'"
            class="operation"
            align="middle"
            justify="center"
          >
            <img src="@/assets/pages-icon/energy/search1.png" alt="" srcset="" />
            <img src="@/assets/pages-icon/energy/place.png" alt="" srcset="" />
          </a-row>
        </template>
      </BaseTable>
    </div>
  </PageCard>
</template>

<script setup lang="ts">
import PageCard from "@/components/PageCard.vue";
import SearchForm from "@/components/SearchForm.vue";
import BaseTable from "@/components/BaseTable.vue";
import { ref, inject, onMounted } from "vue";
import { Emitter } from "mitt";
import { DeviceStatusItem } from "@/models/energy";
import { getDeviceStatusList } from "@/api/energy";
// 获取bus deviceOverview 事件
const bus = inject<Emitter<any>>("bus") as Emitter<any>;

// 获取楼层文件
import BuildingMapper from "@/utils/buildingMapper";
const floors = ref([] as any[]);
const builds = ref(BuildingMapper.getAllBuildings());
const searchFields = ref([
  {
    type: "select" as const,
    prop: "deviceStatus",
    formItem: { label: "在线/离线" },
    attrs: {
      placeholder: "请选择",
      clearable: true,
      size: "large",
    },
    options: [
      { label: "全部", value: "" },
      { label: "未激活", value: "1" },
      { label: "禁用", value: "2" },
      { label: "在线", value: "3" },
      { label: "离线", value: "4" },
    ],
    on: {
      change: (val: any) => {
        initialValues.value.deviceStatus = val;
        getStatusList();
      },
    },
  },
  {
    type: "select" as const,
    prop: "build",
    formItem: { label: "建筑" },
    attrs: {
      size: "large",
      placeholder: "请选择",
    },
    options: builds.value,
    on: {
      change: (val: any) => {
        initialValues.value.build = val;
        initialValues.value.floor = "";
        floors.value = BuildingMapper.getFloorsByBuildingId(val);
        searchFields.value[2].options = floors.value;
        getStatusList();
      },
    },
  },
  {
    type: "select" as const,
    prop: "floor",
    formItem: { label: "楼层" },
    attrs: {
      size: "large",
      placeholder: "请选择",
    },
    options: floors.value,
    on: {
      change: (val: any) => {
        initialValues.value.floor = val;
        getStatusList();
      },
    },
  },
  // {
  //   type: "radio" as const,
  //   prop: "timeRange",
  //   formItem: { label: "" },
  //   showRight: true,
  //   attrs: {
  //     size: "large",
  //   },
  //   options: [
  //     { label: "日", value: "currentDay" },
  //     { label: "月", value: "currentMonth" },
  //     { label: "年", value: "currentYear" },
  //   ],
  //   on: {
  //     change: (val: any) => {
  //       getStatusList();
  //     },
  //   },
  // },
]);
const initialValues = ref({
  deviceStatus: "", //设备状态：1-未激活，2-禁用，3-在线，4-离线
  build: "", //建筑
  floor: "", //楼层
  // deviceType: null, //1电表2水表
});

const columns = [
  { title: "表具编号", dataIndex: "smsLocation" },
  { title: "设备名称", dataIndex: "cabinetNumber" },
  { title: "位置", dataIndex: "measurementArea" },
  { title: "在线/离线", dataIndex: "statusText" },
  { title: "操作", dataIndex: "operation" },
];

// 获取列表数据
const data = ref<DeviceStatusItem[]>([]);
// 添加分页相关响应式变量
const pageNum = ref(1);
const pageSize = ref(20);
const total = ref(0); // 总条数

// 修改获取列表数据的方法
const getStatusList = async () => {
  try {
    const params = {
      ...initialValues.value,
      pageNum: pageNum.value, // 使用当前页码
      pageSize: pageSize.value, // 使用当前页大小
    };
    const res = await getDeviceStatusList(params);
    if (res.code === 200) {
      res.rows.forEach((item: any) => {
        switch (item.status) {
          case 1:
            item.statusText = "未激活";
            break;
          case 2:
            item.statusText = "禁用";
            break;
          case 3:
            item.statusText = "在线";
            break;
          case 4:
            item.statusText = "离线";
            break;
        }
      });
      data.value = res.rows;
      total.value = res.total; // 从接口响应中获取总条数
    } else {
      console.log(res);
    }
  } catch (error) {
    console.error("加载设备概览失败:", error);
  }
};
// 分页变化处理函数
const handlePageChange = (newPage: number, newPageSize: number) => {
  pageNum.value = newPage; // 更新当前页码
  pageSize.value = newPageSize; // 更新当前页码
  getStatusList(); // 重新请求数据
};
getStatusList();
// 监听设备概览事件时也需要重新加载数据
bus.on("deviceOverview", (data: any) => {
  initialValues.value.build = data.buildId;
  initialValues.value.floor = "";
  floors.value = BuildingMapper.getFloorsByBuildingId(data.buildId);
  searchFields.value[2].options = floors.value;
  pageNum.value = 1; // 重置页码为第一页
  getStatusList(); // 重新请求数据
});
onMounted(() => {
  floors.value = BuildingMapper.getFloorsByBuildingId(initialValues.value.build);
  searchFields.value[2].options = floors.value;
});
</script>

<style lang="less" scoped>
.page-card {
  .search-box {
    height: 88px;
    // padding: 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 20px;
    color: #fff;
    span {
      margin-right: 10px;
    }
  }
  .table-container {
    position: relative;
    height: 670px;
    overflow: hidden;
  }
  :deep .ant-table-striped {
    position: relative;
    left: 10px;
    .table-dan {
      height: 57px;
      margin: 8px;
      td {
        background-color: rgba(7, 27, 51, 1);
        color: #fff;
      }
    }
    .table-shuang {
      height: 57px;
      margin: 8px;
      td {
        background-color: rgba(15, 36, 61, 1);
        color: #fff;
      }
    }
    .ant-table-thead {
      th {
        background-color: rgba(15, 41, 82, 1);
        color: #fff;
        height: 67px;
      }
    }
  }

  .ant-table-striped :deep(.ant-table-body) {
    overflow-y: auto !important;
    max-height: 550px !important;
  }

  .ant-table-striped :deep(.ant-table-body::-webkit-scrollbar) {
    position: absolute;
    left: 10px;
    width: 3px;
    height: 8px;
  }
  img {
    margin-right: 20px;
    cursor: pointer;
  }
  img:hover {
    opacity: 0.7;
  }
}
</style>
