import api from "@/utils/request";
import type { ApiResponse } from "@/models/energy";

// 设备信息接口
export interface DeviceInfo {
  id: string;
  name: string;
  type: string;
  model: string;
  manufacturer: string;
  installDate: string;
  location: string;
  status: string;
  power: number;
  voltage: number;
  current: number;
  description?: string;
  maintenanceDate?: string;
  warrantyDate?: string;
  [key: string]: any;
}

// 设备数据查询参数
export interface DeviceDataQuery {
  smsLocation: string; //设备模型对应编码
  startTime: string; //开始时间
  endTime: string; //结束时间
  fieldId: string; //参数
}

/**
 * 获取设备信息
 * @param deviceId 设备ID
 * @returns Promise<ApiResponse<DeviceInfo>>
 */
export const getDeviceInfo = (_deviceId: string) => ({
  code: 200,
  message: "获取设备信息成功",
  data: {
    id: "device-001",
    name: "设备-device-001",
    type: "空调设备",
    model: "AC-2000X",
    manufacturer: "格力电器",
    installDate: "2023-01-15",
    location: "1号楼-3层-东区",
    status: "运行中",
    power: 15.5,
    voltage: 380,
    current: 25.8,
  },
});
// api.get<ApiResponse<DeviceInfo>>(`/api/device/info/${deviceId}`, {});

/**
 * 获取设备图表实时数据
 * @param params 查询参数
 * @returns Promise<ApiResponse<DeviceDataResponse>>
 */
export const getDeviceData = (params: DeviceDataQuery) =>
  api.post<ApiResponse>("/shibomameng/energyManagement/deviceLog", params);

/**
 * 获取查询参数
 * @returns Promise<ApiResponse<DeviceDataQuery>>
 */
export const getQueryParams = (smsLocation: string) =>
  api.post<ApiResponse>("/shibomameng/energyManagement/deviceLogField", { smsLocation });
/**
 * 获取设备系统图
 * @param deviceId 设备ID
 * @returns Promise<ApiResponse<{imageUrl: string}>>
 */
export const getSystemDiagram = (deviceId: string) =>
  api.get<ApiResponse<{ imageUrl: string }>>(`/api/device/diagram/${deviceId}`, {});
