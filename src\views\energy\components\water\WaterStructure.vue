<template>
  <PageCard :title="'用水结构'">
    <div class="search-box">
      <SearchForm :fields="searchFields" :initialValues="initialValues" />
    </div>

    <div class="chart-container">
      <common-chart :echart-obj="chartOption" />
    </div>
  </PageCard>
</template>

<script setup lang="ts">
import PageCard from "@/components/PageCard.vue";
import CommonChart from "@/components/CommonChart.vue";
import SearchForm from "@/components/SearchForm.vue";
import { ref, onMounted } from "vue";
import { getWaterStructure } from "@/api/energy";

// 搜索表单配置
const searchFields = ref([
  {
    type: "radio" as const,
    prop: "timeModel",
    formItem: { label: "" },
    showRight: true,
    attrs: {
      size: "large",
    },
    options: [
      { label: "今日", value: "currentDay" },
      { label: "本月", value: "currentMonth" },
      { label: "本年", value: "currentYear" },
    ],
    on: {
      change: (val: any) => {
        initialValues.value.timeModel = val;
        getCurrentData();
      },
    },
  },
]);
const initialValues = ref({
  timeModel: "currentDay",
});

const getCurrentData = async () => {
  const params = { ...initialValues.value, energyType: "water" };
  try {
    const res = await getWaterStructure(params);
    // console.log("用水结构接口返回：", res);
    if (res.code === 200) {
      // 格式化数据用于饼图
      const formattedData = res.data.map((item: any) => ({
        value: item.groupValue,
        name: item.group,
      }));
      chartOption.value.series[0].data = formattedData;
    } else {
      console.error("获取用水结构数据失败:", res);
    }
    // 你可以在这里处理数据并赋值给 chartOption.value.series[0].data
  } catch (error) {
    console.error("接口请求错误：", error);
  }
};

// 用水结构
const chartOption = ref({
  // tooltip: {
  //   trigger: "item",
  // },
  legend: {
    type: "scroll",
    orient: "vertical",
    right: "5%",
    top: "10%",
    itemHeight: 20,
    itemWidth: 30,
    textStyle: {
      color: "#fff",
      fontSize: "0.2rem",
    },
  },
  tooltip: {
    trigger: "item",
    formatter: "{b}: {c} m³",
    backgroundColor: "rgba(0, 10, 20, 0.8)",
    borderColor: "rgba(255, 255, 255, 0.2)",
    textStyle: {
      color: "#fff",
      fontSize: "0.25rem",
    },
  },

  series: [
    {
      name: "Access From",
      type: "pie",
      radius: ["40%", "80%"],

      center: ["50%", "50%"],
      avoidLabelOverlap: false,
      itemStyle: {
        // borderRadius: 10,
        borderColor: "rgba(0, 0, 0, 0.1)",
        borderWidth: 3,
      },
      label: {
        show: true,
        position: "outside",
        formatter: "{b}\n{c} m³",
        color: "#fff",
        fontSize: "0.2rem",
        fontWeight: "bold",
      },
      labelLine: {
        // 标签的视觉引导线配置
        show: true,
        length: 15,
        length2: 20,
        minTurnAngle: 90,
        lineStyle: {
          width: 3,
          color: "#fff",
        },
      },
      data: [
        { value: 10, name: "公共用水" },
        { value: 20, name: "办公用水" },
        { value: 30, name: "锅炉补水" },
        { value: 15, name: "冷却塔补水" },
        { value: 25, name: "其他" },
      ],
      emphasis: {
        scaleSize: 20, // 高亮后扇区的放大尺寸
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: "rgba(0, 0, 0, 0.5)",
        },
      },
    },
  ],
});
onMounted(getCurrentData);
</script>

<style lang="less" scoped>
.search-box {
  height: 88px;
  // padding-top: 16px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.chart-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 400px;
  > div {
    height: 425px;
    flex: 1;
  }
}
</style>
